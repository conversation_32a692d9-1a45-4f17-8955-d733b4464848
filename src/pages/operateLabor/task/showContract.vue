<template>
  <article>
    <div class="view-picture" style="padding-top: 50px">
      <div
        style="position: relative; border-bottom: 1px solid #eee"
        v-for="(item, index) in pictureUrl"
        :key="index"
      >
      <img
        style="width: 345px; height: 500px; display: block; margin: 0 auto"
        :src="pictureUrl"
        @click="viewPicture"
      />
      </div>
    </div>
    <div
      style="
        position: fixed;
        bottom: 0;
        width: 100%;
        height: 50px;
        background: #fff;
        text-align: center;
      "
    >
      <Button type="primary" @click="handleSign">签署</Button>
    </div>
  </article>
</template>
<script>
import { ImagePreview, Button } from 'vant'

export default {
  components: {
    ImagePreview,
    Button
  },
  data() {
    return {
      pictureUrl: []
    }
  },
  created() {
    const archiveIds = JSON.parse(this.$route.query.archiveId)
    for(var c of archiveIds) {
      this.pictureUrl.push(`${window.env?.apiPath}/api/public/previewFile/${c}`)
    }
    console.log('pictureUrl===', this.pictureUrl)
  },
  methods: {
    viewPicture() {
      ImagePreview([this.pictureUrl])
    },
    handleSign() {
      this.$router.push({
        path: '/laborContractSign',
        query: {
          protocolId: this.$route.query.protocolId
        }
      })
    }
  }
}
</script>
