<template>
  <div class="billing-detail-container" v-loading="loading" element-loading-text="加载中...">
    <div class="detail-container">
      <!-- 账单基本信息 -->
      <div class="summary-container">
        <div class="summary-title">账单详情</div>
        <el-row :gutter="40">
          <el-col :span="8">
            <div class="summary-item">
              <label>账单编号：</label>
              <span>{{ summaryData.billNo || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="summary-item">
              <label>客户：</label>
              <span>{{ summaryData.customerName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="summary-item">
              <label>作业主体：</label>
              <span>{{ summaryData.supplierCorporationName || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="40">
          <el-col :span="8">
            <div class="summary-item">
              <label>服务合同：</label>
              <span>{{ summaryData.contractName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="summary-item">
              <label>账单期间：</label>
              <span>{{ summaryData.billPeriod || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="summary-item">
              <label>账单金额：</label>
              <span>{{ summaryData.totalAmount ? `¥${summaryData.totalAmount}` : '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="40">
          <el-col :span="8">
            <div class="summary-item">
              <label>状态：</label>
              <el-tag
                :type="getStatusTagType(summaryData.status)"
                size="medium"
                effect="light"
              >
                {{ getStatusText(summaryData.status) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="summary-item">
              <label>创建时间：</label>
              <span>{{ formatDateTime(null, null, summaryData.createTime) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="summary-item">
              <label>更新时间：</label>
              <span>{{ formatDateTime(null, null, summaryData.modifyTime) }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 账单明细列表 -->
      <div class="table-container">
        <div class="table-header">
          <div class="table-title">账单明细</div>
        </div>

        <el-table
          :data="tableData"
          :height="tableHeight"
          :stripe="false"
          highlight-current-row
          :header-cell-style="{background:'#f5f7fa', color: '#303133', fontWeight: '550'}"
        >
          <template slot="empty">
            <div class="empty-data">暂无数据</div>
          </template>
          <el-table-column prop="itemName" label="费用项目" width="150" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.itemName || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="itemDescription" label="项目描述" min-width="200" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.itemDescription || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="数量" width="100">
            <template slot-scope="scope">
              {{ scope.row.quantity || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="unitPrice" label="单价" width="120">
            <template slot-scope="scope">
              {{ scope.row.unitPrice ? `¥${scope.row.unitPrice}` : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额" width="120">
            <template slot-scope="scope">
              {{ scope.row.amount ? `¥${scope.row.amount}` : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="taxRate" label="税率" width="100">
            <template slot-scope="scope">
              {{ scope.row.taxRate ? `${scope.row.taxRate}%` : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="taxAmount" label="税额" width="120">
            <template slot-scope="scope">
              {{ scope.row.taxAmount ? `¥${scope.row.taxAmount}` : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="totalAmount" label="含税金额" width="140">
            <template slot-scope="scope">
              {{ scope.row.totalAmount ? `¥${scope.row.totalAmount}` : '-' }}
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            :current-page="Math.floor(conditions.offset / conditions.limit) + 1"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="conditions.limit"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  data() {
    return {
      loading: true,
      summaryData: {},
      tableData: [],
      total: 0,
      tableHeight: 500,
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true
      },
      statusOptions: {
        'PENDING': '待确认',
        'CONFIRMED': '已确认',
        'PAID': '已支付',
        'CANCELLED': '已取消'
      }
    }
  },
  async created() {
    await this.loadDetail()
    this.setTableHeight()
    window.addEventListener('resize', this.setTableHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setTableHeight)
  },
  methods: {
    async loadDetail() {
      this.loading = true
      const id = this.$route.params.id

      const [err, r] = await client.customerGetBillDetail({
        body: { 
          id,
          ...this.conditions
        }
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.summaryData = r.data.summary || {}
      this.tableData = r.data.details || []
      this.total = r.data.total || 0
      
      this.$nextTick(() => {
        this.setTableHeight()
      })
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.loadDetail()
    },

    handleSizeChange(size) {
      this.conditions.limit = size
      this.conditions.offset = 0
      this.loadDetail()
    },

    formatDateTime(row, column, cellValue) {
      if (!cellValue) return '-'
      return new Date(cellValue).toLocaleString('zh-CN')
    },

    getStatusText(status) {
      return this.statusOptions[status] || status || '-'
    },

    getStatusTagType(status) {
      const typeMap = {
        'PENDING': 'warning',
        'CONFIRMED': 'success',
        'PAID': 'success',
        'CANCELLED': 'info'
      }
      return typeMap[status] || 'info'
    },

    setTableHeight() {
      const windowHeight = window.innerHeight;
      const summaryContainer = this.$el.querySelector('.summary-container');
      const summaryHeight = summaryContainer ? summaryContainer.offsetHeight : 0;
      const tableHeader = this.$el.querySelector('.table-header');
      const tableHeaderHeight = tableHeader ? tableHeader.offsetHeight : 0;
      const pagination = this.$el.querySelector('.pagination-container');
      const paginationHeight = pagination ? pagination.offsetHeight : 40;
      const padding = 80;

      const availableHeight = windowHeight - summaryHeight - tableHeaderHeight - paginationHeight - padding;

      if (this.tableData.length <= 5) {
        this.tableHeight = null;
      } else {
        const minHeight = 300;
        const maxHeight = windowHeight - summaryHeight - tableHeaderHeight - paginationHeight - padding - 5;
        this.tableHeight = Math.min(maxHeight, Math.max(availableHeight, minHeight));
      }

      this.tableHeight = Math.floor(this.tableHeight);
    }
  }
}
</script>

<style scoped>
.billing-detail-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: #f5f7fa;
  padding: 12px;
  box-sizing: border-box;
}

.detail-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.summary-container {
  background: #fff;
  padding: 20px;
  margin-bottom: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 0 0 auto;
}

.summary-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
  display: inline-block;
}

.summary-item {
  line-height: 32px;
  font-size: 14px;
  color: #606266;
}

.summary-item label {
  display: inline-block;
  width: 120px;
  text-align: right;
  color: #909399;
  margin-right: 8px;
}

.table-container {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 1;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.el-table {
  margin-bottom: 5px;
  border: none;
}

.el-table th,
.el-table--medium th {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 550;
  text-align: left;
  padding: 4px 0;
  border-bottom: 1px solid #EBEEF5;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.el-table td,
.el-table--medium td {
  padding: 4px 0;
  color: #606266;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.empty-data {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 0;
}

.pagination-container {
  padding: 12px 0 0;
  text-align: right;
  background: #fff;
  margin-top: auto;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .billing-detail-container {
    padding: 12px;
  }
  
  .summary-container {
    padding: 16px;
  }
  
  .table-container {
    padding: 12px;
  }
}
</style>
