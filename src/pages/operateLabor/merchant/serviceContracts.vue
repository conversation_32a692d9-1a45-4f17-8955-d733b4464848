<template>
  <div class="service-contracts-container">
    <!-- 搜索条件 -->
    <div class="search-container">
      <el-form :model="conditions" inline class="search-form">
        <div class="search-header">
          <div class="search-main">
            <el-form-item label="合同:">
              <el-input
                v-model="conditions.filters.name"
                placeholder="请输入合同名称"
                clearable
              />
            </el-form-item>
            <el-form-item label="编号:">
              <el-input
                v-model="conditions.filters.id"
                placeholder="请输入合同编号"
                clearable
                @input="handleIdInput"
              />
            </el-form-item>
            <el-form-item label="状态:">
              <el-select
                v-model="conditions.filters.status"
                placeholder="请选择合同状态"
                clearable
              >
                <el-option label="服务中" value="INIT" />
                <el-option label="提前终止" value="TERMINATION" />
                <el-option label="已到期" value="EXPIRED" />
              </el-select>
            </el-form-item>
            <el-form-item v-show="!needToggle" label="创建时间:">
              <el-date-picker
                v-model="createTimeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                @change="handleCreateTimeChange"
              />
            </el-form-item>
          </div>
          <div class="search-actions">
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="onReset">重置</el-button>
            <el-button v-show="needToggle" type="text" class="toggle-btn" @click="toggleMoreFilters">
              {{ showMoreFilters ? '收起' : '展开' }}
              <i :class="showMoreFilters ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            </el-button>
          </div>
        </div>

        <div v-show="needToggle && showMoreFilters" class="more-form-items">
          <el-form-item label="创建时间:">
            <el-date-picker
              v-model="createTimeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              @change="handleCreateTimeChange"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="data"
        v-loading="loading"
        :height="tableHeight"
        :stripe="false"
        highlight-current-row
        :header-cell-style="{background:'#f5f7fa', color: '#303133', fontWeight: '550'}"
      >
        <template slot="empty">
          <div class="empty-data">暂无数据</div>
        </template>
        <el-table-column prop="id" label="合同编号" min-width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.id || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="合同名称" min-width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.name || '-' }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="customerName" label="客户" min-width="120" /> -->
        <el-table-column prop="supplierName" label="作业主体" min-width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.supplierName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="开始日期" min-width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.startDate || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="endDate" label="结束日期" min-width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.endDate || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.createTime || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="合同状态" min-width="100">
          <template slot-scope="scope">
            <el-tag 
              :type="getStatusTagType(scope.row.status)" 
              size="medium"
              effect="light"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template slot-scope="scope">
            <div class="action-buttons">
              <el-button
                type="text"
                size="small"
                @click="handleView(scope.row)"
              >
                查看
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :current-page="Math.floor(conditions.offset / conditions.limit) + 1"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="conditions.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  data() {
    return {
      createTimeRange: [],
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        filters: {
          name: '',
          id: '',
          createTimeStart: null,
          createTimeEnd: null,
          status: ''
        }
      },
      total: 0,
      data: [],
      loading: true,
      showMoreFilters: false,
      tableHeight: 500,
      needToggle: false,
      resizeTimer: null
    }
  },
  async created() {
    await this.getList()
    this.setTableHeight()
    window.addEventListener('resize', this.handleResize)
  },
  mounted() {
    this.$nextTick(() => {
      this.checkNeedToggle()
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer)
    }
  },
  methods: {
    async getList() {
      this.loading = true

      const [err, r] = await client.customerListContract({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
      this.$nextTick(() => {
        this.setTableHeight()
      })
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },
    
    handleSizeChange(size) {
      this.conditions.limit = size
      this.conditions.offset = 0
      this.getList()
    },

    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },

    onReset() {
      this.conditions = {
        offset: 0,
        limit: 10,
        withTotal: true,
        filters: {
          name: '',
          id: '',
          createTimeStart: null,
          createTimeEnd: null,
          status: ''
        }
      }
      this.createTimeRange = []
      this.getList()
    },

    handleView(row) {
      this.$router.push(`/serviceContracts/${row.id}`)
    },
    getStatusText(status) {
      const statusMap = {
        INIT: '服务中',
        TERMINATION: '提前终止',
        EXPIRED: '已到期'
      }
      return statusMap[status] || status
    },

    getStatusTagType(status) {
      const typeMap = {
        INIT: 'success',
        TERMINATION: 'danger',
        EXPIRED: 'info'
      }
      return typeMap[status] || 'info'
    },
    
    handleCreateTimeChange(value) {
      if (value && value.length === 2) {
        this.conditions.filters.createTimeStart = value[0]
        this.conditions.filters.createTimeEnd = value[1]
      } else {
        this.conditions.filters.createTimeStart = null
        this.conditions.filters.createTimeEnd = null
      }
    },
    
    toggleMoreFilters() {
      this.showMoreFilters = !this.showMoreFilters
      this.$nextTick(() => {
        this.setTableHeight()
      })
    },
    
    handleIdInput() {
      this.conditions.filters.id = this.conditions.filters.id.replace(/[^\d]/g, '');
    },
    
    handleResize() {
      // 防抖处理，避免频繁触发
      clearTimeout(this.resizeTimer)
      this.resizeTimer = setTimeout(() => {
        this.setTableHeight()
        this.checkNeedToggle()
      }, 150)
    },

    checkNeedToggle() {
      this.$nextTick(() => {
        try {
          const searchMain = this.$el?.querySelector('.search-main')
          const searchActions = this.$el?.querySelector('.search-actions')
          if (!searchMain || !searchActions) return

          const mainFormItems = searchMain.querySelectorAll('.el-form-item')
          let totalWidth = 0

          mainFormItems.forEach(item => {
            if (item.style.display !== 'none' && getComputedStyle(item).display !== 'none') {
              // 确保元素已渲染完成
              const itemWidth = item.offsetWidth || 200 // 如果获取不到宽度，使用默认值
              totalWidth += itemWidth + 24
            }
          })

          const searchContainer = this.$el?.querySelector('.search-container')
          const actionsWidth = searchActions.offsetWidth + 20 // 动态获取操作按钮宽度
          const containerWidth = searchContainer ? searchContainer.offsetWidth : window.innerWidth
          const availableWidth = containerWidth - actionsWidth - 40 // 额外预留40px边距

          this.needToggle = totalWidth > availableWidth

          if (!this.needToggle) {
            this.showMoreFilters = false
          }
        } catch (error) {
          console.warn('检测展开/收起状态时出错:', error);
          // 发生错误时，根据屏幕宽度简单判断
          this.needToggle = window.innerWidth < 1200
        }
      })
    },

    setTableHeight() {
      try {
        const windowHeight = window.innerHeight;
        const searchContainer = this.$el?.querySelector('.search-container');
        const searchHeight = searchContainer ? searchContainer.offsetHeight : 0;
        const pagination = this.$el?.querySelector('.pagination-container');
        const paginationHeight = pagination ? pagination.offsetHeight : 40;
        const padding = 40;

        const availableHeight = windowHeight - searchHeight - paginationHeight - padding;

        if (this.data.length <= 5) {
          this.tableHeight = null;
        } else {
          const minHeight = 300;
          const maxHeight = windowHeight - searchHeight - paginationHeight - padding - 5;
          this.tableHeight = Math.min(maxHeight, Math.max(availableHeight, minHeight));
        }

        if (this.tableHeight) {
          this.tableHeight = Math.floor(this.tableHeight);
        }
      } catch (error) {
        console.warn('设置表格高度时出错:', error);
        this.tableHeight = 500; // 设置默认高度
      }
    }
  }
}
</script>

<style scoped>
.service-contracts-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: #f5f7fa;
  padding: 12px;
  box-sizing: border-box;
}

.search-container {
  background: #fff;
  padding: 20px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  position: relative;
}

.search-form {
  position: relative;
}

.search-header {
  position: relative;
  padding-right: 200px;
  min-height: 40px;
}

.search-main {
  display: flex;
  flex-wrap: wrap;
  gap: 16px 24px;
  align-items: center;
}

.search-main .el-form-item {
  margin: 0;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.search-main .el-form-item .el-form-item__label {
  margin-right: 8px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
}

.search-main .el-form-item .el-form-item__content {
  width: 200px; 
}

.search-main .el-form-item:last-child .el-form-item__content {
  width: 160px; 
}

.search-actions {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  height: 40px;
}

.search-actions .el-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
}

.search-actions .toggle-btn {
  padding: 8px 4px;
}



.more-form-items {
  padding-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px 24px;
  align-items: center;
}

.more-form-items .el-form-item {
  margin: 0;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.more-form-items .el-form-item .el-form-item__label {
  margin-right: 8px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
}

.more-form-items .el-form-item .el-form-item__content {
  width: 210px; /* 缩小30%: 300px * 0.7 = 210px */
}



/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 3px;
  transition: all 0.2s;
  min-width: 48px;
  height: 24px;
  line-height: 16px;
}

.action-buttons .el-button--text {
  background: transparent;
  border: none;
  padding: 4px 8px;
}

.action-buttons .el-button--text:hover {
  background: #ecf5ff;
  transform: translateY(-1px);
}

.table-container {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 1;
  overflow: hidden;
}

.el-table {
  margin-bottom: 5px;
  border: none;
}

.el-table th,
.el-table--medium th {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 550; /* 表头文字粗细改为500 */
  text-align: left;
  padding: 4px 0;
  border-bottom: 1px solid #EBEEF5;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.el-table td,
.el-table--medium td {
  padding: 4px 0;
  color: #606266;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.pagination-container {
  padding: 12px 0 0;
  text-align: right;
  background: #fff;
  margin-top: auto;
}

.empty-data {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 0;
}

/* 响应式调整 - 智能展开/收起功能 */
/* 大屏幕：输入框缩小30%，保持单行布局 */
@media screen and (min-width: 1200px) {
  .search-header {
    padding-right: 200px;
  }

  .search-main .el-form-item .el-form-item__content {
    width: 140px; /* 缩小30%: 200px * 0.7 = 140px */
  }

  /* 状态选择器稍微小一点，为其他条件让出空间 */
  .search-main .el-form-item:last-child .el-form-item__content {
    width: 112px; /* 缩小30%: 160px * 0.7 = 112px */
  }
}

/* 中等屏幕：输入框缩小30%，保持单行布局 */
@media screen and (max-width: 1199px) {
  .search-header {
    padding-right: 180px;
  }

  .search-main .el-form-item .el-form-item__content {
    width: 126px; /* 缩小30%: 180px * 0.7 = 126px */
  }
}

@media screen and (max-width: 992px) {
  .search-header {
    padding-right: 160px;
  }

  .search-main .el-form-item .el-form-item__content {
    width: 112px; /* 缩小30%: 160px * 0.7 = 112px */
  }
}

/* 小屏幕：保持单行布局，输入框缩小30% */
@media screen and (max-width: 768px) {
  .search-header {
    padding-right: 140px;
  }

  .search-main {
    gap: 8px 16px; /* 减小间距以适应更多元素 */
  }

  .search-main .el-form-item .el-form-item__content {
    width: 98px; /* 缩小30%: 140px * 0.7 = 98px */
  }

  .more-form-items .el-form-item .el-form-item__content {
    width: 147px; /* 缩小30%: 210px * 0.7 = 147px */
  }
}

/* 超小屏幕：查询按钮移到下方，保持单行布局 */
@media screen and (max-width: 480px) {
  .search-header {
    padding-right: 0;
    min-height: auto;
  }

  .search-actions {
    position: static;
    margin-top: 16px;
    justify-content: flex-start;
    height: auto;
  }

  .search-main {
    gap: 4px 8px; /* 进一步减小间距 */
  }

  .search-main .el-form-item .el-form-item__content {
    width: 80px; /* 进一步缩小以适应小屏幕 */
  }

  .more-form-items .el-form-item .el-form-item__content {
    width: 120px; /* 进一步缩小以适应小屏幕 */
  }
}
</style>
