<template>
  <div class="labor-new-container" v-loading="pageLoading" element-loading-text="加载中...">
    <div class="form-container">
      <el-form :model="form" :rules="rules" ref="form" label-width="120px" class="labor-form">
        
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">添加人员</div>
          
          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="姓名" prop="name">
                <el-input 
                  v-model="form.name" 
                  placeholder="请输入姓名" 
                  maxlength="20"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="身份证件号码" prop="idCard">
                <el-input 
                  v-model="form.idCard" 
                  placeholder="请输入身份证件号码"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="手机号" prop="cellPhone">
                <el-input 
                  v-model="form.cellPhone" 
                  placeholder="请输入手机号"
                  @input="handlePhoneInput"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属作业主体" prop="supplierId">
                <el-select
                  v-model="form.supplierId"
                  placeholder="请选择所属作业主体"
                  style="width: 100%"
                  filterable
                  @change="onSupplierChange"
                >
                  <el-option 
                    v-for="supplier in supplierOptions" 
                    :key="supplier.id" 
                    :label="supplier.name"
                    :value="supplier.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="所属客户" prop="customerId">
                <el-select
                  v-model="form.customerId"
                  placeholder="请选择所属客户"
                  style="width: 100%"
                  filterable
                  @change="onCustomerChange"
                >
                  <el-option 
                    v-for="customer in customerOptions" 
                    :key="customer.id" 
                    :label="customer.name"
                    :value="customer.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属服务合同" prop="contractId">
                <el-select
                  v-model="form.contractId"
                  placeholder="请选择所属服务合同"
                  style="width: 100%"
                  filterable
                  :disabled="!form.supplierId || !form.customerId"
                >
                  <el-option 
                    v-for="contract in contractOptions" 
                    :key="contract.id" 
                    :label="contract.name"
                    :value="contract.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="银行卡号" prop="bankCard">
                <el-input 
                  v-model="form.bankCard" 
                  placeholder="请输入银行卡号"
                  @input="handleBankCardInput"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户行" prop="cardBank">
                <el-input 
                  v-model="form.cardBank" 
                  placeholder="请输入开户行" 
                  maxlength="64"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button @click="onCancel">取消</el-button>
          <el-button type="primary" @click="onSubmit" :loading="submitting">确定</el-button>
        </div>

      </el-form>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import handleSuccess from '../../../helpers/handleSuccess'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  data() {
    return {
      pageLoading: true,
      submitting: false,
      form: {
        name: '',
        idCard: '',
        cellPhone: '',
        supplierId: '',
        customerId: '',
        contractId: '',
        bankCard: '',
        cardBank: ''
      },
      rules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        idCard: [
          { required: true, message: '请输入身份证件号码', trigger: 'blur' },
          { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号码', trigger: 'blur' }
        ],
        cellPhone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        supplierId: [{ required: true, message: '请选择所属作业主体', trigger: 'change' }],
        customerId: [{ required: true, message: '请选择所属客户', trigger: 'change' }],
        contractId: [{ required: true, message: '请选择所属服务合同', trigger: 'change' }],
        bankCard: [{ required: true, message: '请输入银行卡号', trigger: 'blur' }],
        cardBank: [{ required: true, message: '请输入开户行', trigger: 'blur' }]
      },
      supplierOptions: [],
      customerOptions: [],
      contractOptions: []
    }
  },
  async created() {
    try {
      await this.loadOptions()
    } finally {
      this.pageLoading = false
    }
  },
  methods: {
    async loadOptions() {
      // 加载作业主体选项
      const [supplierErr, supplierRes] = await client.customerGetSuppliers({ body: {} })
      if (!supplierErr) {
        this.supplierOptions = supplierRes.data || []
      }

      // 加载客户选项
      const [customerErr, customerRes] = await client.customerGetCustomers({ body: {} })
      if (!customerErr) {
        this.customerOptions = customerRes.data || []
      }
    },

    async onSupplierChange() {
      this.form.contractId = ''
      this.contractOptions = []
      await this.loadContracts()
    },

    async onCustomerChange() {
      this.form.contractId = ''
      this.contractOptions = []
      await this.loadContracts()
    },

    async loadContracts() {
      if (!this.form.supplierId || !this.form.customerId) {
        return
      }

      const [err, res] = await client.customerGetContractsBySupplierAndCustomer({
        body: {
          supplierId: this.form.supplierId,
          customerId: this.form.customerId
        }
      })

      if (!err) {
        this.contractOptions = res.data || []
      }
    },

    handlePhoneInput() {
      // 只允许输入数字
      this.form.cellPhone = this.form.cellPhone.replace(/[^\d]/g, '')
    },

    handleBankCardInput() {
      // 只允许输入数字
      this.form.bankCard = this.form.bankCard.replace(/[^\d]/g, '')
    },

    async onSubmit() {
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }

      this.submitting = true

      const [err] = await client.customerAddLabor({ body: this.form })

      this.submitting = false

      if (err) {
        handleError(err)
        return
      }

      handleSuccess('添加成功')
      this.$router.push('/laborInfo')
    },

    onCancel() {
      this.$router.back()
    }
  }
}
</script>

<style scoped>
.labor-new-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
  padding: 40px 20px;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  box-sizing: border-box;
}

.form-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  width: 100%;
  max-width: 900px;
  min-height: 500px;
}

.labor-form {
  width: 100%;
}

.form-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 24px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
  display: inline-block;
}

.el-form-item {
  margin-bottom: 24px;
}

.el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: 550;
  line-height: 32px;
}

.el-input,
.el-select {
  width: 100%;
}

.form-actions {
  text-align: center;
  padding-top: 32px;
  border-top: 1px solid #ebeef5;
  margin-top: 40px;
}

.form-actions .el-button {
  margin: 0 12px;
  padding: 12px 32px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s;
}

.form-actions .el-button--primary {
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.form-actions .el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.form-actions .el-button:not(.el-button--primary) {
  color: #606266;
  border-color: #dcdfe6;
  background-color: #fff;
}

.form-actions .el-button:not(.el-button--primary):hover {
  color: #409EFF;
  border-color: #409EFF;
  background-color: #ecf5ff;
  transform: translateY(-1px);
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .labor-new-container {
    padding: 20px 12px;
  }
  
  .form-container {
    padding: 24px;
  }
  
  .el-form-item__label {
    width: 100px !important;
  }
}
</style>
