<template>
  <div class="enhanced-file-list">
    <div v-if="loading" class="loading-state">
      <div class="loading-content">
        <i class="el-icon-loading loading-icon"></i>
        <span class="loading-text">加载文件信息中...</span>
      </div>
    </div>
    
    <div v-else-if="fileList.length === 0" class="empty-state">
      <div class="empty-content">
        <i class="el-icon-folder-opened empty-icon"></i>
        <p class="empty-text">暂无附件</p>
      </div>
    </div>
    
    <div v-else class="file-cards">
      <div 
        v-for="(file, index) in fileList" 
        :key="index" 
        class="file-card"
        @click="downloadFile(file)"
      >
        <div class="file-card-header">
          <div class="file-icon-wrapper">
            <i :class="getFileIcon(file.name)" class="file-icon"></i>
          </div>
          <div class="file-actions">
            <el-tooltip content="下载文件" placement="top">
              <el-button 
                type="text" 
                size="small" 
                class="download-btn"
                @click.stop="downloadFile(file)"
              >
                <i class="el-icon-download"></i>
              </el-button>
            </el-tooltip>
          </div>
        </div>
        
        <div class="file-card-body">
          <div class="file-name" :title="file.name">
            {{ file.name }}
          </div>
          <div class="file-meta">
            <span class="file-type">{{ getFileType(file.name) }}</span>
          </div>
        </div>
        
        <div class="file-card-footer">
          <span class="download-hint">点击下载</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import makeClient from '../../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  name: 'EnhancedFileList',
  props: {
    fileIds: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fileList: [],
      loading: false
    }
  },
  watch: {
    fileIds: {
      immediate: true,
      handler(newVal) {
        this.loadFileList(newVal)
      }
    }
  },
  methods: {
    async loadFileList(fileIds) {
      if (!fileIds) {
        this.fileList = []
        return
      }

      const ids = fileIds.split(',').filter(id => id.trim())
      if (ids.length === 0) {
        this.fileList = []
        return
      }

      this.loading = true
      this.fileList = []

      try {
        const fileInfoPromises = ids.map(async id => {
          try {
            const [err, response] = await client.describeFile({
              body: { id: id.trim() }
            })

            if (response && response.success && response.data) {
              return {
                id: id.trim(),
                name: response.data.name || `文件${id}`,
                url: `${window.env?.apiPath}/api/public/downloadFile/${id.trim()}`
              }
            } else {
              return {
                id: id.trim(),
                name: `文件${id}`,
                url: `${window.env?.apiPath}/api/public/downloadFile/${id.trim()}`
              }
            }
          } catch (error) {
            console.error('获取文件信息失败：', error)
            return {
              id: id.trim(),
              name: `文件${id}`,
              url: `${window.env?.apiPath}/api/public/downloadFile/${id.trim()}`
            }
          }
        })

        const fileInfos = await Promise.all(fileInfoPromises)
        this.fileList = fileInfos.filter(info => info !== null)
      } catch (error) {
        console.error('加载文件列表失败：', error)
        this.$message.error('加载文件列表失败')
      } finally {
        this.loading = false
      }
    },

    getFileIcon(fileName) {
      const ext = fileName.split('.').pop()?.toLowerCase()
      const iconMap = {
        'pdf': 'el-icon-document',
        'doc': 'el-icon-document',
        'docx': 'el-icon-document',
        'xls': 'el-icon-s-grid',
        'xlsx': 'el-icon-s-grid',
        'ppt': 'el-icon-present',
        'pptx': 'el-icon-present',
        'jpg': 'el-icon-picture',
        'jpeg': 'el-icon-picture',
        'png': 'el-icon-picture',
        'gif': 'el-icon-picture',
        'zip': 'el-icon-folder-opened',
        'rar': 'el-icon-folder-opened',
        'txt': 'el-icon-document-copy'
      }
      return iconMap[ext] || 'el-icon-document'
    },

    getFileType(fileName) {
      const ext = fileName.split('.').pop()?.toLowerCase()
      const typeMap = {
        'pdf': 'PDF文档',
        'doc': 'Word文档',
        'docx': 'Word文档',
        'xls': 'Excel表格',
        'xlsx': 'Excel表格',
        'ppt': 'PPT演示',
        'pptx': 'PPT演示',
        'jpg': '图片文件',
        'jpeg': '图片文件',
        'png': '图片文件',
        'gif': '图片文件',
        'zip': '压缩文件',
        'rar': '压缩文件',
        'txt': '文本文件'
      }
      return typeMap[ext] || '未知类型'
    },

    downloadFile(file) {
      try {
        const link = document.createElement('a')
        link.href = file.url
        link.download = file.name
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        this.$message.success(`开始下载：${file.name}`)
      } catch (error) {
        console.error('下载文件失败：', error)
        this.$message.error('下载文件失败')
      }
    }
  }
}
</script>

<style scoped>
.enhanced-file-list {
  width: 100%;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #909399;
}

.loading-icon {
  font-size: 32px;
  margin-bottom: 12px;
  animation: rotate 2s linear infinite;
}

.loading-text {
  font-size: 14px;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #c0c4cc;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  margin: 0;
}

/* 文件卡片网格 */
.file-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 10fr));
  gap: 10px;
  padding: 8px 0px 0px 8px;
}

/* 文件卡片 */
.file-card {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.file-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.file-card:hover .file-card-footer {
  opacity: 1;
  transform: translateY(0);
}

/* 卡片头部 */
.file-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.file-icon-wrapper {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #409eff, #66b1ff);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-icon {
  font-size: 24px;
  color: #fff;
}

.file-actions {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.file-card:hover .file-actions {
  opacity: 1;
}

.download-btn {
  color: #409eff !important;
  font-size: 18px;
  padding: 8px !important;
}

.download-btn:hover {
  color: #66b1ff !important;
  background: rgba(64, 158, 255, 0.1) !important;
}

/* 卡片主体 */
.file-card-body {
  margin-bottom: 16px;
}

.file-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1.4;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

.file-meta {
  display: flex;
  align-items: center;
}

.file-type {
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
}

/* 卡片底部 */
.file-card-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #409eff, #66b1ff);
  color: #fff;
  text-align: center;
  padding: 8px;
  font-size: 12px;
  opacity: 0;
  transform: translateY(100%);
  transition: all 0.3s ease;
}

.download-hint {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .file-card {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .file-cards {
    gap: 12px;
  }
  
  .file-card {
    padding: 12px;
  }
  
  .file-icon-wrapper {
    width: 40px;
    height: 40px;
  }
  
  .file-icon {
    font-size: 20px;
  }
  
  .file-name {
    font-size: 14px;
  }
}
</style>
