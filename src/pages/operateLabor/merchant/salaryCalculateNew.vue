<template>
  <div class="salary-new-container" v-loading="pageLoading" element-loading-text="加载中...">
    <div class="form-container">
      <el-form :model="form" :rules="rules" ref="form" label-width="120px" class="salary-form">
        
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">新增工资表</div>
          
          <el-form-item label="客户" prop="customerId">
            <el-select
              v-model="form.customerId"
              placeholder="请选择客户"
              style="width: 100%"
              filterable
              @change="onCustomerChange"
            >
              <el-option 
                v-for="customer in customerOptions" 
                :key="customer.id" 
                :label="customer.name"
                :value="customer.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="作业主体" prop="supplierCorporationId">
            <el-select
              v-model="form.supplierCorporationId"
              placeholder="请选择作业主体"
              style="width: 100%"
              filterable
              @change="onSupplierChange"
            >
              <el-option 
                v-for="supplier in supplierOptions" 
                :key="supplier.id" 
                :label="supplier.name"
                :value="supplier.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="服务合同" prop="contractId">
            <el-select
              v-model="form.contractId"
              placeholder="请选择服务合同"
              style="width: 100%"
              filterable
              :disabled="!form.customerId || !form.supplierCorporationId"
            >
              <el-option 
                v-for="contract in contractOptions" 
                :key="contract.id" 
                :label="contract.name"
                :value="contract.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="税款所属期" prop="taxPeriod">
            <el-date-picker
              v-model="form.taxPeriod"
              type="month"
              placeholder="选择月份"
              format="yyyy-MM"
              value-format="yyyy-MM"
              style="width: 100%"
              @change="onTaxPeriodChange"
            />
          </el-form-item>

          <el-form-item label="个税申报月">
            <el-input :value="taxDeclarationMonth" readonly />
          </el-form-item>

          <el-form-item label="上传应发文件" prop="file">
            <input
              ref="fileInput"
              type="file"
              @change="handleFileChange"
              style="display: none"
              accept=".xlsx, .xls"
            />
            <div style="display: flex; align-items: center; width: 100%">
              <el-input
                :value="form.file ? form.file.name : ''"
                placeholder="请选择文件"
                readonly
                style="flex-grow: 1"
              >
                <el-button slot="append" @click="triggerFileInput">
                  选择文件
                </el-button>
              </el-input>
              <el-button
                type="text"
                @click="downloadTemplate"
                style="margin-left: 10px"
              >
                下载模板
              </el-button>
            </div>
          </el-form-item>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button type="primary" @click="onSubmit" :loading="submitting">
            创建
          </el-button>
          <el-button @click="onCancel">取消</el-button>
        </div>

      </el-form>
    </div>

    <!-- 错误数据对话框 -->
    <el-dialog
      title="应发文件导入结果"
      :visible.sync="showErrorDialog"
      width="500px"
    >
      <div v-if="importResult.success">
        <p style="color: #67c23a;">
          <i class="el-icon-success"></i>
          文件导入成功！共导入 {{ importResult.successCount }} 条数据。
        </p>
      </div>
      <div v-else>
        <p style="color: #f56c6c;">
          <i class="el-icon-error"></i>
          您有 <span style="color: red; margin: 0 5px">{{ importResult.failCount }}</span> 条数据导入失败，
          <span style="color: green; cursor: pointer" @click="downloadErrorFile">
            点击下载错误文件
          </span>
        </p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showErrorDialog = false">关闭</el-button>
        <el-button v-if="importResult.success" type="primary" @click="goToDetail">
          查看详情
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import handleSuccess from '../../../helpers/handleSuccess'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  data() {
    return {
      pageLoading: true,
      submitting: false,
      form: {
        customerId: '',
        supplierCorporationId: '',
        contractId: '',
        taxPeriod: '',
        file: null
      },
      rules: {
        customerId: [{ required: true, message: '请选择客户', trigger: 'change' }],
        supplierCorporationId: [{ required: true, message: '请选择作业主体', trigger: 'change' }],
        contractId: [{ required: true, message: '请选择服务合同', trigger: 'change' }],
        taxPeriod: [{ required: true, message: '请选择税款所属期', trigger: 'change' }],
        file: [{ required: true, message: '请选择应发文件', trigger: 'change' }]
      },
      customerOptions: [],
      supplierOptions: [],
      contractOptions: [],
      taxDeclarationMonth: '',
      showErrorDialog: false,
      importResult: {
        success: false,
        successCount: 0,
        failCount: 0,
        payrollId: null,
        errorFileUrl: ''
      }
    }
  },
  async created() {
    try {
      await this.loadOptions()
    } finally {
      this.pageLoading = false
    }
  },
  methods: {
    async loadOptions() {
      // 加载客户选项
      const [customerErr, customerRes] = await client.customerGetCustomers({ body: {} })
      if (!customerErr) {
        this.customerOptions = customerRes.data || []
      }

      // 加载作业主体选项
      const [supplierErr, supplierRes] = await client.customerGetSuppliers({ body: {} })
      if (!supplierErr) {
        this.supplierOptions = supplierRes.data || []
      }
    },

    async onCustomerChange() {
      this.form.contractId = ''
      this.contractOptions = []
      await this.loadContracts()
    },

    async onSupplierChange() {
      this.form.contractId = ''
      this.contractOptions = []
      await this.loadContracts()
    },

    async loadContracts() {
      if (!this.form.customerId || !this.form.supplierCorporationId) {
        return
      }

      const [err, res] = await client.customerGetContractsBySupplierAndCustomer({
        body: {
          supplierId: this.form.supplierCorporationId,
          customerId: this.form.customerId
        }
      })

      if (!err) {
        this.contractOptions = res.data || []
      }
    },

    onTaxPeriodChange() {
      if (this.form.taxPeriod) {
        // 计算个税申报月（税款所属期的下一个月）
        const [year, month] = this.form.taxPeriod.split('-')
        const nextMonth = parseInt(month) + 1
        if (nextMonth > 12) {
          this.taxDeclarationMonth = `${parseInt(year) + 1}-01`
        } else {
          this.taxDeclarationMonth = `${year}-${nextMonth.toString().padStart(2, '0')}`
        }
      } else {
        this.taxDeclarationMonth = ''
      }
    },

    triggerFileInput() {
      this.$refs.fileInput.click()
    },

    handleFileChange(event) {
      const file = event.target.files[0]
      if (file) {
        this.form.file = file
        this.$refs.form.validateField('file')
      }
    },

    async downloadTemplate() {
      try {
        const [err, res] = await client.customerDownloadPayrollTemplate({ body: {} })
        if (err) {
          handleError(err)
          return
        }
        
        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([res]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', '工资表模板.xlsx')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } catch (error) {
        handleError(error)
      }
    },

    async onSubmit() {
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }

      this.submitting = true

      const formData = new FormData()
      formData.append('customerId', this.form.customerId)
      formData.append('supplierCorporationId', this.form.supplierCorporationId)
      formData.append('contractId', this.form.contractId)
      formData.append('taxPeriod', this.form.taxPeriod)
      formData.append('file', this.form.file)

      const [err, res] = await client.customerAddPayroll({ 
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      this.submitting = false

      if (err) {
        handleError(err)
        return
      }

      this.importResult = res.data
      this.showErrorDialog = true

      if (this.importResult.success) {
        handleSuccess('工资表创建成功')
      }
    },

    async downloadErrorFile() {
      if (this.importResult.errorFileUrl) {
        window.open(this.importResult.errorFileUrl, '_blank')
      }
    },

    goToDetail() {
      if (this.importResult.payrollId) {
        this.$router.push(`/salaryCalculate/${this.importResult.payrollId}`)
      } else {
        this.$router.push('/salaryCalculate')
      }
    },

    onCancel() {
      this.$router.back()
    }
  }
}
</script>

<style scoped>
.salary-new-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
  padding: 40px 20px;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  box-sizing: border-box;
}

.form-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  width: 100%;
  max-width: 700px;
  min-height: 500px;
}

.salary-form {
  width: 100%;
}

.form-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 24px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
  display: inline-block;
}

.el-form-item {
  margin-bottom: 24px;
}

.el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: 550;
  line-height: 32px;
}

.el-input,
.el-select,
.el-date-picker {
  width: 100%;
}

.form-actions {
  text-align: center;
  padding-top: 32px;
  border-top: 1px solid #ebeef5;
  margin-top: 40px;
}

.form-actions .el-button {
  margin: 0 12px;
  padding: 12px 32px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s;
}

.form-actions .el-button--primary {
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.form-actions .el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.form-actions .el-button:not(.el-button--primary) {
  color: #606266;
  border-color: #dcdfe6;
  background-color: #fff;
}

.form-actions .el-button:not(.el-button--primary):hover {
  color: #409EFF;
  border-color: #409EFF;
  background-color: #ecf5ff;
  transform: translateY(-1px);
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .salary-new-container {
    padding: 20px 12px;
  }
  
  .form-container {
    padding: 24px;
  }
  
  .el-form-item__label {
    width: 100px !important;
  }
}
</style>
