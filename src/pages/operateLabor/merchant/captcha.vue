<template>
  <div :style="{ display: 'flex' }">
    <el-input
      v-model="captcha.value"
      placeholder="请输入图形验证码"
      :style="{ flex: 1, marginRight: '10px' }"
      @change="emitToken"
      :disabled="!captcha.token"
    ></el-input>
    <div v-if="!captcha.token" :style="{ width: '100px', height: '40px', backgroundColor: '#f5f5f5', display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px solid #dcdfe6', borderRadius: '4px' }">
      <i class="el-icon-loading" style="color: #999;"></i>
    </div>
    <img v-else @click="refreshCaptcha" :src="captchaImage" alt="captcha" @load="onImageLoad" :style="{ cursor: 'pointer' }" />
  </div>
</template>

<script>
import handleError from 'kit/helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'Captcha',
  computed: {
    captchaImage() {
      return `${
        window.env?.apiPath
      }/api/public/captcha?token=${encodeURIComponent(this.captcha.token)}`
    }
  },
  data() {
    return {
      captcha: {
        token: '',
        value: ''
      }
    }
  },
  mounted() {
    this.refreshCaptcha()
  },
  methods: {
    async refreshCaptcha() {
      const [err, r] = await client.createCaptcha()
      if (err) {
        handleError(err)
        return
      }

      this.captcha.token = r.data
      this.captcha.value = '' // 清空输入值
      this.emitToken()
      // 等待下一个tick，确保图片开始加载
      this.$nextTick(() => {
        // 如果图片已经加载完成，直接发出事件
        if (this.captchaImage) {
          this.$emit('captcha-loaded')
        }
      })
    },
    emitToken() {
      this.$emit('input', this.captcha)
    },
    onImageLoad() {
      this.$emit('captcha-loaded')
    }
  }
}
</script>
