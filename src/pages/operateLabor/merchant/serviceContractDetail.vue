<template>
  <div class="contract-detail-container" v-loading="loading" element-loading-text="加载中...">
    <div class="form-container">
      <el-form :model="contractData" :rules="rules" ref="form" label-width="120px" class="detail-form">

        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">基本信息</div>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="合同名称">
                <el-input v-model="contractData.name" readonly placeholder="合同名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="作业主体">
                <el-input v-model="contractData.supplierName" readonly placeholder="作业主体" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="合同有效期">
                <el-input v-model="contractData.contractPeriod" readonly placeholder="合同有效期" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 发票信息 -->
        <div class="form-section">
          <div class="section-title">发票信息</div>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="发票抬头">
                <el-input v-model="contractData.invoiceTitle" readonly placeholder="发票抬头" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纳税人识别号">
                <el-input v-model="contractData.invoiceTaxNo" readonly placeholder="纳税人识别号" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="开户银行">
                <el-input v-model="contractData.invoiceBankName" readonly placeholder="开户银行" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="银行账号">
                <el-input v-model="contractData.invoiceBankAccount" readonly placeholder="银行账号" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="注册地址">
                <el-input v-model="contractData.invoiceRegisterAddress" readonly placeholder="注册地址" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话">
                <el-input v-model="contractData.invoiceCompanyTel" readonly placeholder="联系电话" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="24">
              <el-form-item label="发票备注">
                <el-input type="textarea" v-model="contractData.invoiceRemark" readonly placeholder="发票备注" :rows="3" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 管理费计算 -->
        <div class="form-section">
          <div class="section-title">管理费计算</div>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="计算规则">
                <el-input :value="getCalculationRuleText(contractData.manageCalculationRule)" readonly placeholder="计算规则" />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="contractData.manageCalculationRule === 'EMPLOYEE_COUNT'">
              <el-form-item label="服务费金额">
                <el-input :value="contractData.manageAmount ? `${contractData.manageAmount}元` : ''" readonly placeholder="服务费金额" />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="contractData.manageCalculationRule === 'PAYABLE_AMOUNT_RATE'">
              <el-form-item label="费率">
                <el-input :value="contractData.manageRate ? `${contractData.manageRate}%` : ''" readonly placeholder="费率" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 合同附件 -->
        <div class="form-section">
          <div class="section-title">合同附件</div>

          <div class="file-list-wrapper">
            <EnhancedFileList :fileIds="contractData.fileIds" />
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button @click="onBack" size="medium">
            返回
          </el-button>
        </div>

      </el-form>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import EnhancedFileList from './components/EnhancedFileList.vue'

const client = makeClient()

export default {
  components: { EnhancedFileList },
  data() {
    return {
      loading: true,
      contractId: null,
      contractData: {},
      rules: {}
    }
  },

  async created() {
    // 获取路由参数中的合同ID
    this.contractId = this.$route.params.id

    // 加载合同数据
    await this.loadContractData()
  },

  methods: {
    // 加载合同数据
    async loadContractData() {
      try {
        const [err, response] = await client.customerQueryContract({
          body: { id: this.contractId }
        })

        if(err){
          this.$message.error(err.message)
          this.loading = false
          return
        }
        if (response.success) {
          this.contractData = response.data || {}
          // 格式化合同有效期显示
          if (this.contractData.startDate && this.contractData.endDate) {
            this.contractData.contractPeriod = `${this.contractData.startDate} 至 ${this.contractData.endDate}`
          }
        } else {
          this.$message.error(response.message || '获取合同信息失败')
        }
        this.loading = false
      } catch (error) {
        handleError(error)
        this.loading = false
      }
    },

    // 获取计算规则文本
    getCalculationRuleText(rule) {
      const ruleMap = {
        'EMPLOYEE_COUNT': '按雇员人数计算',
        'PAYABLE_AMOUNT_RATE': '按应发金额比例计算'
      }
      return ruleMap[rule] || rule
    },

    getStatusText(status) {
      const statusMap = {
        INIT: '服务中',
        TERMINATION: '提前终止',
        EXPIRED: '已到期'
      }
      return statusMap[status] || status
    },

    getStatusTagType(status) {
      const typeMap = {
        INIT: 'success',
        TERMINATION: 'danger',
        EXPIRED: 'info'
      }
      return typeMap[status] || 'info'
    },

    // 返回
    onBack() {
      this.$router.back()
    }
  }
}
</script>

<style scoped>
.contract-detail-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
  padding: 15px 0px 0px 0px;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  box-sizing: border-box;
}

.form-container {
  background: #fff;
  border-radius: 8px;
  width: 100%;
  max-width: 1000px;
  min-height: 500px;
}

.detail-form {
  width: 100%;
}

.form-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 24px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
  display: inline-block;
}

.el-form-item {
  margin-bottom: 24px;
}

.el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: 550;
  line-height: 32px;
}

.el-input {
  width: 100%;
}

.form-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.form-actions .el-button {
  margin: 0 12px;
  padding: 12px 32px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s;
}

.form-actions .el-button:not(.el-button--primary) {
  color: #606266;
  border-color: #dcdfe6;
  background-color: #fff;
}

.form-actions .el-button:not(.el-button--primary):hover {
  color: #409EFF;
  border-color: #409EFF;
  background-color: #ecf5ff;
  transform: translateY(-1px);
}

/* 文件列表包装器 */
.file-list-wrapper {
  margin-top: 8px;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .contract-detail-container {
    padding: 20px 12px;
  }

  .form-container {
    padding: 24px;
  }

  .el-form-item__label {
    width: 100px !important;
  }
}
</style>
