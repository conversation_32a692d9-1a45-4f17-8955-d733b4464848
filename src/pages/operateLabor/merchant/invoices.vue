<template>
  <div class="invoices-container">
    <!-- 搜索条件 -->
    <div class="search-container">
      <el-form :model="conditions" inline class="search-form">
        <div class="search-header">
          <div class="search-main">
            <el-form-item label="发票申请ID:">
              <el-input
                v-model="conditions.filters.id"
                placeholder="请输入发票申请ID"
                clearable
                @input="handleIdInput"
              />
            </el-form-item>
            <el-form-item label="所属客户:">
              <el-select
                v-model="conditions.filters.customerId"
                placeholder="请选择所属客户"
                clearable
                filterable
              >
                <el-option
                  v-for="item in customerOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间:">
              <el-date-picker
                v-model="createTimeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                @change="handleCreateTimeChange"
              />
            </el-form-item>
          </div>
          <div class="search-actions">
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="onReset">重置</el-button>
            <el-button type="text" class="toggle-btn" @click="toggleMoreFilters">
              {{ showMoreFilters ? '收起' : '展开' }}
              <i :class="showMoreFilters ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            </el-button>
          </div>
        </div>

        <div v-show="showMoreFilters" class="more-form-items">
          <el-form-item label="开票主体:">
            <el-select
              v-model="conditions.filters.supplierCorporationId"
              placeholder="请选择开票主体"
              clearable
              filterable
            >
              <el-option
                v-for="item in supplierOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="发票状态:">
            <el-select
              v-model="conditions.filters.status"
              placeholder="请选择发票状态"
              clearable
            >
              <el-option
                v-for="(label, value) in statusOptions"
                :key="value"
                :label="label"
                :value="value"
              />
            </el-select>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="data"
        v-loading="loading"
        :height="tableHeight"
        :stripe="false"
        highlight-current-row
        :header-cell-style="{background:'#f5f7fa', color: '#303133', fontWeight: '550'}"
      >
        <template slot="empty">
          <div class="empty-data">暂无数据</div>
        </template>
        <el-table-column prop="id" label="发票申请ID" width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.id || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="customerName" label="所属客户" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.customerName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="supplierCorporationName" label="开票主体" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.supplierCorporationName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="invoiceType" label="发票类型" width="100">
          <template slot-scope="scope">
            {{ getInvoiceTypeText(scope.row.invoiceType) }}
          </template>
        </el-table-column>
        <el-table-column prop="totalAmount" label="开票金额" width="120">
          <template slot-scope="scope">
            {{ scope.row.totalAmount ? `¥${scope.row.totalAmount}` : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="taxRate" label="税率" width="80">
          <template slot-scope="scope">
            {{ scope.row.taxRate ? `${scope.row.taxRate}%` : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag
              :type="getStatusTagType(scope.row.status)"
              size="medium"
              effect="light"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="申请时间" min-width="150" :formatter="formatDateTime" />
        <el-table-column prop="modifyTime" label="更新时间" min-width="150" :formatter="formatDateTime" />

        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <div class="action-buttons">
              <el-button
                type="text"
                size="small"
                @click="handleView(scope.row)"
              >
                查看
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :current-page="Math.floor(conditions.offset / conditions.limit) + 1"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="conditions.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  data() {
    return {
      createTimeRange: [],
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        filters: {
          id: '',
          customerId: '',
          supplierCorporationId: '',
          status: '',
          createTimeStart: null,
          createTimeEnd: null
        }
      },
      total: 0,
      data: [],
      loading: true,
      showMoreFilters: false,
      tableHeight: 500,
      customerOptions: [],
      supplierOptions: [],
      statusOptions: {
        'PENDING': '待处理',
        'PROCESSING': '处理中',
        'COMPLETED': '已完成',
        'REJECTED': '已拒绝'
      },
      invoiceTypeOptions: {
        'ORDINARY': '普通发票',
        'SPECIAL': '专用发票'
      }
    }
  },
  async created() {
    await this.loadOptions()
    await this.getList()
    this.setTableHeight()
    window.addEventListener('resize', this.setTableHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setTableHeight)
  },
  methods: {
    async loadOptions() {
      // 加载客户选项
      const [customerErr, customerRes] = await client.customerGetCustomers({ body: {} })
      if (!customerErr) {
        this.customerOptions = customerRes.data || []
      }

      // 加载作业主体选项
      const [supplierErr, supplierRes] = await client.customerGetSuppliers({ body: {} })
      if (!supplierErr) {
        this.supplierOptions = supplierRes.data || []
      }
    },

    async getList() {
      this.loading = true

      const [err, r] = await client.customerGetInvoices({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
      this.$nextTick(() => {
        this.setTableHeight()
      })
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },

    handleSizeChange(size) {
      this.conditions.limit = size
      this.conditions.offset = 0
      this.getList()
    },

    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },

    onReset() {
      this.conditions = {
        offset: 0,
        limit: 10,
        withTotal: true,
        filters: {
          id: '',
          customerId: '',
          supplierCorporationId: '',
          status: '',
          createTimeStart: null,
          createTimeEnd: null
        }
      }
      this.createTimeRange = []
      this.getList()
    },

    handleIdInput() {
      // 只允许输入数字
      this.conditions.filters.id = this.conditions.filters.id.replace(/[^\d]/g, '')
    },

    formatDateTime(row, column, cellValue) {
      if (!cellValue) return '-'
      return new Date(cellValue).toLocaleString('zh-CN')
    },

    getStatusText(status) {
      return this.statusOptions[status] || status || '-'
    },

    getStatusTagType(status) {
      const typeMap = {
        'PENDING': 'warning',
        'PROCESSING': 'primary',
        'COMPLETED': 'success',
        'REJECTED': 'danger'
      }
      return typeMap[status] || 'info'
    },

    getInvoiceTypeText(type) {
      return this.invoiceTypeOptions[type] || type || '-'
    },

    handleView(row) {
      this.$router.push(`/invoices/${row.id}`)
    },

    handleCreateTimeChange(value) {
      if (value && value.length === 2) {
        this.conditions.filters.createTimeStart = value[0]
        this.conditions.filters.createTimeEnd = value[1]
      } else {
        this.conditions.filters.createTimeStart = null
        this.conditions.filters.createTimeEnd = null
      }
    },

    toggleMoreFilters() {
      this.showMoreFilters = !this.showMoreFilters
      this.$nextTick(() => {
        this.setTableHeight()
      })
    },

    setTableHeight() {
      const windowHeight = window.innerHeight;
      const searchContainer = this.$el.querySelector('.search-container');
      const searchHeight = searchContainer ? searchContainer.offsetHeight : 0;
      const pagination = this.$el.querySelector('.pagination-container');
      const paginationHeight = pagination ? pagination.offsetHeight : 40;
      const padding = 40;

      const availableHeight = windowHeight - searchHeight - paginationHeight - padding;

      if (this.data.length <= 5) {
        this.tableHeight = null;
      } else {
        const minHeight = 300;
        const maxHeight = windowHeight - searchHeight - paginationHeight - padding - 5;
        this.tableHeight = Math.min(maxHeight, Math.max(availableHeight, minHeight));
      }

      this.tableHeight = Math.floor(this.tableHeight);
    }
  }
}
</script>

<style scoped>
.invoices-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: #f5f7fa;
  padding: 12px;
  box-sizing: border-box;
}

.search-container {
  background: #fff;
  padding: 20px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  position: relative;
}

.search-form {
  position: relative;
}

.search-header {
  position: relative;
  padding-right: 200px;
  min-height: 40px;
}

.search-main {
  display: flex;
  flex-wrap: wrap;
  gap: 16px 24px;
  align-items: center;
}

.search-main .el-form-item {
  margin: 0;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.search-main .el-form-item .el-form-item__label {
  margin-right: 8px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
}

.search-main .el-form-item .el-form-item__content {
  width: 200px;
}

.search-actions {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  height: 40px;
}

.search-actions .el-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
}

.search-actions .toggle-btn {
  color: #409eff;
  padding: 8px 4px;
}

.el-form-item .el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: 550;
  line-height: 32px;
}

.el-form-item .el-input,
.el-form-item .el-select,
.el-form-item .el-date-editor {
  width: 100%;
}

.more-form-items {
  padding-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px 24px;
  align-items: center;
}

.more-form-items .el-form-item {
  margin: 0;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.more-form-items .el-form-item .el-form-item__label {
  margin-right: 8px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
}

.more-form-items .el-form-item .el-form-item__content {
  width: 300px;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.more-form-items .el-form-item {
  width: calc(50% - 20px);
  margin-right: 20px;
  margin-bottom: 16px;
  box-sizing: border-box;
}

.more-form-items .el-date-editor.el-input__inner,
.more-form-items .el-date-editor.el-input,
.more-form-items .el-date-editor.el-range-editor {
  width: 100%;
  min-width: 300px;
}

.toggle-btn {
  padding: 0;
  font-size: 14px;
  margin-left: 16px;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
}

.toggle-btn:hover {
  transform: translateY(-1px);
}

.table-container {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 1;
  overflow: hidden;
}

.el-table {
  margin-bottom: 5px;
  border: none;
}

.el-table th,
.el-table--medium th {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 550;
  text-align: left;
  padding: 4px 0;
  border-bottom: 1px solid #EBEEF5;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.el-table td,
.el-table--medium td {
  padding: 4px 0;
  color: #606266;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 3px;
  transition: all 0.2s;
  min-width: 48px;
  height: 24px;
  line-height: 16px;
}

.action-buttons .el-button--text {
  background: transparent;
  border: none;
  padding: 4px 8px;
}

.action-buttons .el-button--text:hover {
  background: #ecf5ff;
  transform: translateY(-1px);
}

.empty-data {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 0;
}

.pagination-container {
  padding: 12px 0 0;
  text-align: right;
  background: #fff;
  margin-top: auto;
}

/* 响应式调整 */
@media screen and (max-width: 1400px) {
  .more-form-items .el-form-item {
    width: calc(50% - 20px);
  }
}

@media screen and (max-width: 1200px) {
  .search-header {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .search-main {
    flex: 1;
    min-width: 70%;
    margin-bottom: 16px;
  }

  .search-actions {
    justify-content: flex-end;
    min-width: 200px;
  }

  .more-form-items .el-form-item {
    width: calc(100% - 20px);
  }
}

/* 响应式调整 */
@media screen and (max-width: 1400px) {
  .search-main {
    margin-right: 180px;
  }

  .more-form-items .el-form-item {
    width: calc(33.33% - 20px);
  }
}

@media screen and (max-width: 1200px) {
  .search-main {
    margin-right: 160px;
  }

  .search-main .el-form-item {
    min-width: 260px;
  }

  .more-form-items .el-form-item {
    width: calc(50% - 20px);
  }
}

@media screen and (max-width: 992px) {
  .search-main {
    margin-right: 140px;
  }

  .search-main .el-form-item {
    min-width: 240px;
    margin-bottom: 12px;
  }

  .search-main .el-form-item .el-input,
  .search-main .el-form-item .el-select {
    width: 200px;
  }
}

@media screen and (max-width: 768px) {
  .search-header {
    flex-direction: column;
  }

  .search-main {
    margin-right: 0;
    margin-bottom: 16px;
    width: 100%;
  }

  .search-main .el-form-item {
    width: 100%;
    margin-right: 0;
    margin-bottom: 12px;
    min-width: auto;
  }

  .search-main .el-form-item .el-input,
  .search-main .el-form-item .el-select {
    width: 100%;
  }

  .search-actions {
    position: static;
    justify-content: flex-start;
    width: 100%;
  }

  .search-actions .el-button {
    margin-left: 0;
    margin-right: 8px;
    margin-bottom: 8px;
  }

  .more-form-items .el-form-item {
    width: 100%;
    margin-right: 0;
  }

  .more-form-items .el-date-editor.el-input__inner,
  .more-form-items .el-date-editor.el-input,
  .more-form-items .el-date-editor.el-range-editor {
    width: 100%;
    min-width: auto;
  }
}

@media screen and (max-width: 480px) {
  .search-actions {
    flex-wrap: wrap;
  }

  .search-actions .el-button {
    flex: 1;
    min-width: 60px;
    margin-bottom: 8px;
  }
}
</style>
