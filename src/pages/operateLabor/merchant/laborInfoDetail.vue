<template>
  <div class="labor-detail-container" v-loading="loading" element-loading-text="加载中...">
    <div class="form-container">
      <!-- 编辑按钮 -->
      <div class="action-header">
        <div v-if="!isEditMode">
          <el-button type="primary" @click="toggleEditMode">
            <i class="el-icon-edit"></i>
            编辑
          </el-button>
        </div>
      </div>

      <el-form :model="formData" :rules="rules" ref="form" label-width="120px" class="detail-form">
        
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">基本信息</div>
          
          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="姓名" prop="name">
                <el-input 
                  v-model="formData.name" 
                  :readonly="!isEditMode" 
                  placeholder="请输入姓名" 
                  maxlength="20"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号" prop="cellphone">
                <el-input 
                  v-model="formData.cellphone" 
                  :readonly="!isEditMode" 
                  :disabled="isEditMode" 
                  placeholder="请输入手机号"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="身份证件号码" prop="idCard">
                <el-input 
                  v-model="formData.idCard" 
                  :readonly="!isEditMode" 
                  :disabled="isEditMode" 
                  placeholder="请输入身份证件号码"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="年龄">
                <el-input 
                  v-model="formData.age" 
                  :readonly="!isEditMode" 
                  :disabled="isEditMode" 
                  placeholder="年龄"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="证件有效期">
                <el-input 
                  v-model="formData.idCardPeriod" 
                  :readonly="!isEditMode" 
                  placeholder="证件有效期"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="性别">
                <el-input 
                  v-model="formData.gender" 
                  :readonly="!isEditMode" 
                  :disabled="isEditMode" 
                  placeholder="性别"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="民族">
                <el-input 
                  v-model="formData.nation" 
                  :readonly="!isEditMode" 
                  placeholder="民族"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="详细地址">
                <el-input 
                  v-model="formData.householdAddress" 
                  :readonly="!isEditMode" 
                  placeholder="详细地址"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="银行卡号" prop="bankCard">
                <el-input 
                  v-model="formData.bankCard" 
                  :readonly="!isEditMode" 
                  placeholder="请输入银行卡号"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户行">
                <el-input 
                  v-model="formData.cardBank" 
                  :readonly="!isEditMode" 
                  placeholder="开户行" 
                  maxlength="64"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 编辑模式下的操作按钮 -->
        <div v-if="isEditMode" class="form-actions">
          <el-button @click="cancelEdit">取消</el-button>
          <el-button type="primary" @click="confirmEdit" :loading="submitting">确定</el-button>
        </div>

      </el-form>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import handleSuccess from '../../../helpers/handleSuccess'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  data() {
    return {
      loading: true,
      isEditMode: false,
      submitting: false,
      formData: {
        name: '',
        cellphone: '',
        idCard: '',
        age: '',
        idCardPeriod: '',
        gender: '',
        nation: '',
        householdAddress: '',
        bankCard: '',
        cardBank: ''
      },
      originalData: {},
      rules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        cellphone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        idCard: [
          { required: true, message: '请输入身份证件号码', trigger: 'blur' },
          { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号码', trigger: 'blur' }
        ],
        bankCard: [{ required: true, message: '请输入银行卡号', trigger: 'blur' }]
      }
    }
  },
  async created() {
    await this.loadDetail()
  },
  methods: {
    async loadDetail() {
      this.loading = true
      const id = this.$route.params.id

      const [err, r] = await client.customerGetLaborDetail({
        body: { id }
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.formData = { ...r.data }
      this.originalData = { ...r.data }
    },

    toggleEditMode() {
      this.isEditMode = true
    },

    cancelEdit() {
      this.isEditMode = false
      this.formData = { ...this.originalData }
    },

    async confirmEdit() {
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }

      this.submitting = true

      const [err] = await client.customerUpdateLaborDetail({
        body: {
          id: this.$route.params.id,
          ...this.formData
        }
      })

      this.submitting = false

      if (err) {
        handleError(err)
        return
      }

      handleSuccess('更新成功')
      this.isEditMode = false
      this.originalData = { ...this.formData }
    }
  }
}
</script>

<style scoped>
.labor-detail-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
  padding: 40px 20px;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  box-sizing: border-box;
}

.form-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  width: 100%;
  max-width: 900px;
  min-height: 500px;
}

.action-header {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 30px;
}

.action-header .el-button {
  border-radius: 4px;
  padding: 9px 20px;
  transition: all 0.3s;
}

.action-header .el-button--primary {
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.action-header .el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.detail-form {
  width: 100%;
}

.form-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 24px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
  display: inline-block;
}

.el-form-item {
  margin-bottom: 24px;
}

.el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: 550;
  line-height: 32px;
}

.el-input {
  width: 100%;
}

.form-actions {
  text-align: center;
  padding-top: 32px;
  border-top: 1px solid #ebeef5;
  margin-top: 40px;
}

.form-actions .el-button {
  margin: 0 12px;
  padding: 12px 32px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s;
}

.form-actions .el-button--primary {
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.form-actions .el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.form-actions .el-button:not(.el-button--primary) {
  color: #606266;
  border-color: #dcdfe6;
  background-color: #fff;
}

.form-actions .el-button:not(.el-button--primary):hover {
  color: #409EFF;
  border-color: #409EFF;
  background-color: #ecf5ff;
  transform: translateY(-1px);
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .labor-detail-container {
    padding: 20px 12px;
  }
  
  .form-container {
    padding: 24px;
  }
  
  .el-form-item__label {
    width: 100px !important;
  }
}
</style>
