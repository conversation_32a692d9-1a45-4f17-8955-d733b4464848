<template>
  <div class="relatedContracts">
    <div
      class="group"
      :key="index"
      v-for="(group, index) in groups"
      style="margin-bottom: 10px"
    >
      <div style="font-size: 14px; margin-bottom: 10px">
        {{ modificationType2string(group.type) }}合同
      </div>
      <div
        class="contract"
        style="margin-bottom: 10px"
        :key="contract.contractId"
        v-for="contract in group.contracts"
      >
        <div style="display: flex; justify-self: space">
          <span class="text-ellipsis" style="flex: 1 1 auto; height: 20px">{{
            contract.name
          }}</span>
          <Status :status="contract.status" style="flex: 0 0 46px" />
        </div>
        <span
          v-if="contract.signFinishTime"
          style="color: #96979b; font-size: 12px"
        >
          签署日期: {{ contract.signFinishTime | formatDateTime }}
        </span>
      </div>
    </div>
  </div>
</template>
<script>
import Status from './status.vue'
import modificationType2string from '../../../services/contract/modificationType2string'
export default {
  components: {
    Status
  },
  created() {
    if (!this.contracts) {
      return
    }
    var r = {}
    for (var c of this.contracts) {
      r[c.type] = r[c.type] || []
      r[c.type].push(c)
    }
    for (var key in r) {
      this.groups.push({
        type: key,
        contracts: r[key]
      })
    }
    console.log('this.groups', this.groups)
  },
  props: {
    contracts: Array
  },
  data() {
    return {
      groups: []
    }
  },
  methods: { modificationType2string }
}
</script>