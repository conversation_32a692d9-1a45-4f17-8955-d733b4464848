<template>
  <Tag :bgColor="bgColor" :color="color" :status="statusStr" />
</template>
<script>
import status2string from '../../../services/contract/status2string'
import Tag from './tag.vue'
import {
  ContractStatusOverdue,
  ContractStatusCompleted,
  ContractStatusRejected,
  ContractStatusWithdrew
} from '../../../services/contract/constants'
export default {
  components: {
    Tag
  },
  props: {
    status: String
  },
  computed: {
    statusStr() {
      return status2string(this.status)
    },
    bgColor() {
      switch (this.status) {
        case ContractStatusCompleted:
        case ContractStatusRejected:
        case ContractStatusOverdue:
        case ContractStatusWithdrew:
          return '#F8F8F8' //gray
        default:
          return '#F7FAFD' //blue
      }
    },
    color() {
      switch (this.status) {
        case ContractStatusCompleted:
        case ContractStatusRejected:
        case ContractStatusOverdue:
        case ContractStatusWithdrew:
          return '#777C94' //gray
        default:
          return '#4F71FF' //blue
      }
    }
  }
}
</script>