<template>
  <div v-if="color">
    <div style="position: relative; background-color: #fff; z-index: 1">
      <img
        src="../../../assets/images/pic_seal_blue.png"
        v-if="color === 'blue'"
      />
      <img
        src="../../../assets/images/pic_seal_gray.png"
        v-if="color === 'gray'"
      />
      <img
        src="../../../assets/images/pic_seal_red.png"
        v-if="color === 'red'"
      />
      <span
        :style="{
          position: 'absolute',
          top: '6px',
          left: statusStr.length === 4 ? '8px' : '16px',
          fontSize: '16px',
          fontWeight: 600,
          color: color
        }"
        >{{ statusStr }}</span
      >
    </div>
  </div>
</template>
<script>
import {
  ContractStatusCompleted,
  ContractStatusWithdrew,
  ContractStatusRejected,
  ContractProcessStatusIneffective,
  ContractProcessStatusEffective,
  ContractProcessStatusHaveExpired,
  ContractProcessStatusCancelled,
  ContractStatusOverdue
} from '../../../services/contract/constants'
//这里是特殊的 混合了status 与 processStatus
const status2string = (status, processStatus) => {
  switch (status) {
    case ContractStatusRejected:
      return '审核驳回'
    case ContractStatusWithdrew:
      return '已撤回'
    case ContractStatusOverdue:
      return '已逾期'
  }
  if (status === ContractStatusCompleted) {
    switch (processStatus) {
      case ContractProcessStatusIneffective:
        return '未生效'
      case ContractProcessStatusEffective:
        return '生效中'
      case ContractProcessStatusHaveExpired:
        return '到期终止'
      case ContractProcessStatusCancelled:
        return '提前终止'
    }
  }
}
const colorOfStatus = (status, processStatus) => {
  switch (status) {
    case ContractStatusWithdrew:
      return 'gray'
    case ContractStatusRejected:
    case ContractStatusOverdue:
      return 'red'
  }
  if (status === ContractStatusCompleted) {
    switch (processStatus) {
      case ContractProcessStatusIneffective:
        return 'gray'
      case ContractProcessStatusHaveExpired:
      case ContractProcessStatusCancelled:
        return 'red'
      case ContractProcessStatusEffective:
        return 'blue'
    }
  }

  return ''
}
export default {
  props: {
    status: String,
    processStatus: String
  },
  computed: {
    statusStr() {
      return status2string(this.status, this.processStatus)
    },
    color() {
      const color = colorOfStatus(this.status, this.processStatus)
      return color
    }
  },
  methods: {}
}
</script>