<template>
  <span
    :style="{
      background: bgColor,
      color: color,
      fontSize: '12px'
    }"
  >
    {{ statusStr }}
  </span>
</template>
<script>
import signProcessStatus2string from '../../../services/contract/signProcessStatus2string'
import {
  ContractWriteProcessStatusWaitingSend,
  ContractWriteProcessStatusWaitingWrite,
  ContractWriteProcessStatusWrote
} from '../../../services/contract/constants'
export default {
  props: {
    status: String
  },
  computed: {
    statusStr() {
      return signProcessStatus2string(this.status)
    },
    bgColor() {
      switch (this.status) {
        case ContractWriteProcessStatusWaitingWrite:
          return '#FFF3F3' //red
        case ContractWriteProcessStatusWrote:
          return '#F0FBF0' //green
        default:
          return '#F8F8F8' //gray
      }
    },
    color() {
      switch (this.status) {
        case ContractWriteProcessStatusWaitingWrite:
          return '#F63939' //red
        case ContractWriteProcessStatusWrote:
          return '#07BB06' //green
        default:
          return '##777C94' //gray
      }
    }
  }
}
</script>