<template>
  <div
    style="width: 696px; margin: 24px 0 70px 0; color: #24262a; font-size: 14px"
  >
    <div class="row">
      <div>
        <span class="info-label">合同名称</span
        ><span class="info-value">{{ contractInfo.name }}</span>
      </div>
      <div>
        <span class="info-label">合同开始日期</span
        ><span class="info-value">{{
          contractInfo.startTime
            ? formatDateTime('yyyy-MM-dd', contractInfo.startTime)
            : '-'
        }}</span>
      </div>
    </div>
    <div class="row">
      <div>
        <span class="info-label">合同结束日期</span
        ><span class="info-value">{{
          contractInfo.endTime
            ? formatDateTime('yyyy-MM-dd', contractInfo.endTime)
            : '-'
        }}</span>
      </div>
      <div>
        <span class="info-label">签署日期</span
        ><span class="info-value">{{
          contractInfo.signFinishTime
            ? formatDateTime('yyyy-MM-dd', contractInfo.signFinishTime)
            : '-'
        }}</span>
      </div>
    </div>
    <div style="flex: 50% 0 0; display: flex">
      <span class="info-label">签署方</span>
      <div
        class="text-ellipsis-2line"
        :title="signers(contractInfo.signerList)"
        style="width: 260px; font-size: 12px"
      >
        <!-- <SignProcessList :value="{ signerList: contractInfo.signerList }" /> -->
        {{ signers(contractInfo.signerList) }}
      </div>
    </div>
  </div>
</template>

<script>
import formatDateTime from '../../../formatters/dateTime'
import SignProcessList from '../signing/signProcessList.vue'
import { SingerTypePerson } from '../../../services/contract/constants'
export default {
  name: 'BasicInfo',
  components: {
    SignProcessList
  },
  props: {
    contractInfo: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    signers(signerList = []) {
      let signerText = ''
      for (let signer of signerList) {
        if (signer.signerType === SingerTypePerson) {
          signerText += `${signer.signer && signer.signer.name}，`
        } else {
          signerText += `${signer.legal && signer.legal.name} (${
            signer.signer && signer.signer.name
          })，`
        }
      }
      return signerText.substring(0, signerText.length - 1)
    },
    formatDateTime
  }
}
</script>

<style scoped>
.row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}
.info-label {
  display: inline-block;
  width: 100px;
  text-align: right;
  line-height: 14px;
  font-size: 14px;
  color: #777c94;
  margin-right: 24px;
}
.info-value {
  display: inline-block;
  width: 200px;
}
</style>