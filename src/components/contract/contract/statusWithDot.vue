<template>
  <TextWithDot :text="statusStr" :color="bgColor" :sameColor="true" />
</template>
<script>
import status2string from '../../../services/contract/status2string'
import TextWithDot from '../textWithDot.vue'
import {
  ContractStatusOverdue,
  ContractStatusCompleted,
  ContractStatusRejected,
  ContractStatusWithdrew
} from '../../../services/contract/constants'
export default {
  components: {
    TextWithDot
  },
  props: {
    status: String
  },
  computed: {
    statusStr() {
      return status2string(this.status)
    },
    bgColor() {
      switch (this.status) {
        case ContractStatusCompleted:
          return '#549f55' //green
        case ContractStatusRejected:
        case ContractStatusOverdue:
        case ContractStatusWithdrew:
          return '#ec808d' //red
        default:
          return '#4d74ff' //blue
      }
    }
  }
}
</script>