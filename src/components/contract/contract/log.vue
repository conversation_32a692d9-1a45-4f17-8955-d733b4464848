<template>
  <StepsWithDashedLine :steps="logs">
    <template v-slot="{ item }">
      <div style="display: flex; color: #aeadb4">
        <span style="0 0 95px">{{
          item.createTime | formatDateTime('yyyy-MM-dd HH:mm')
        }}</span>
        <span style="flex: 1; text-align: right">{{
          logType2string(item.type)
        }}</span>
      </div>
      <span v-if="item.operator.name">{{ item.operator.name }}</span>
      <span v-if="item.operator.mobile">({{ item.operator.mobile }})</span>
      <div
        style="
          background: #f8f8f8;
          color: #777c94;
          padding: 8px 5px;
          border-radius: 8px;
        "
        v-if="item.remark"
      >
        {{ item.remark }}
      </div>
    </template>
  </StepsWithDashedLine>
</template>
<script>
import StepsWithDashedLine from '../stepsWithDashedLine.vue'
import logType2string from '../../../services/contract/logType2string'
export default {
  components: {
    StepsWithDashedLine
  },
  methods: { logType2string },
  // created() {
  //   console.log('typeof ', this.logs[0], isArray(this.logs))
  // },
  props: {
    logs: Array
  }
}
</script>