<template>
  <div
    class="filePagesPagination"
    :style="{
      display: 'flex',
      height: '40px',
      lineHeight: '52px',
      boxShadow: '0 -2px 4px rgb(227 230 233 / 50%)',
      background: '#fff',
      alignItems: 'center',
      fontSize: '12px',
      position: 'sticky',
      bottom: 0,
      padding: '0 30px'
    }"
  >
    <i
      class="el-icon-d-arrow-left"
      :style="{
        color: current === 1 ? '#ccc' : ''
      }"
      @click="goPage(1)"
    />
    <i
      class="el-icon-arrow-left"
      @click="goPage(current - 1)"
      :style="{
        color: current === 1 ? '#ccc' : ''
      }"
    />
    <div
      :style="{
        display: 'flex',
        alignItems: 'center'
      }"
    >
      <div>
        前往
        <input
          :value="current"
          :style="{
            width: '30px',
            height: '24px',
            border: '1px solid #EEF0F4',
            borderRadius: '4px',
            textAlign: 'center'
          }"
          ref="pageCustom"
          @keydown="keydown"
        />
        /{{ total }} 页
      </div>
      <el-button
        type="primary"
        plain
        :style="{
          width: '56px',
          height: '24px',
          padding: 0,
          margin: '0 5px',
          fontSize: '12px'
        }"
        @click="goCustomPage"
      >
        确定
      </el-button>
    </div>
    <i
      class="el-icon-arrow-right"
      @click="goPage(current + 1)"
      :style="{
        color: current === total ? '#ccc' : ''
      }"
    />
    <i
      class="el-icon-d-arrow-right"
      @click="goPage(total)"
      :style="{
        color: current === total ? '#ccc' : ''
      }"
    />
    <div
      :style="{
        flex: '1 1 auto',
        textAlign: 'center'
      }"
    >
      <div>
        <span>Tips: 使用</span>
        <i class="el-icon-top" style="cursor: default" />
        <i class="el-icon-bottom" style="cursor: default" />
        <i class="el-icon-back" style="cursor: default" />
        <i class="el-icon-right" style="cursor: default" />
        <span>进行位置调整</span>
      </div>
    </div>
    <div
      :style="{
        display: 'flex',
        alignItems: 'center'
      }"
    >
      <i
        class="el-icon-minus"
        @click="zoomDown"
        :style="{
          color: scale === '75.00' ? '#ccc' : ''
        }"
      />
      <input
        :value="scale"
        :style="{
          width: '48px',
          height: '24px',
          border: '1px solid #EEF0F4',
          borderRadius: '4px',
          fontSize: '12px',
          textAlign: 'left'
        }"
        @change="zoomCustom"
      />
      <span
        :style="{
          position: 'relative',
          left: '-15px'
        }"
        >%</span
      >
      <i
        class="el-icon-plus"
        :style="{
          position: 'relative',
          left: '-10px',
          color: scale === '150.00' ? '#ccc' : ''
        }"
        @click="zoomUp"
      />
    </div>
  </div>
</template>
<script>
// current, total, scale, onPageChange, onScaleChange
// const defaultScales = [0.25, 0.33, 0.5, 0.67, 0.75, 1.0, 1.25, 1.5, 2.0, 4.0]
const defaultScales = [0.75, 1.0, 1.25, 1.5]
const getCloestMinus = scale => {
  for (var i = 0; i < defaultScales.length; i++) {
    if (defaultScales[i] >= scale) {
      if (i === 0) {
        return defaultScales[0]
      }
      return defaultScales[i - 1]
    }
  }
}
const getCloestPlus = scale => {
  const l = defaultScales.length
  for (var i = 0; i < defaultScales.length; i++) {
    if (i === defaultScales.length - 1) {
      return defaultScales[l - 1]
    }
    if (defaultScales[i] > scale) {
      return defaultScales[i] > defaultScales[l - 1]
        ? defaultScales[l - 1]
        : defaultScales[i]
    }
  }
}
export default {
  mounted() {
    const _this = this
    document.getElementById('pagesBox').onscroll = e => {
      const top = e.target.scrollTop / this.currentScale
      const height = this.imgSize[1] + 16

      _this.current = parseInt(top / height) + 1
      // console.log(top, height, _this.current)
    }
  },
  computed: {
    scale() {
      if (!this.currentScale) {
        return ''
      }

      const p = this.currentScale * 100
      return p.toFixed(2)
    }
  },
  props: {
    total: Number,
    currentScale: Number,
    imgSize: Array
  },
  data() {
    return {
      current: 1
    }
  },
  methods: {
    isDisabled(page) {
      // if(page === )
    },
    goCustomPage() {
      const el = this.$refs.pageCustom
      var v = parseInt(el.value)
      if (!v) {
        v = 1
      }
      if (v < 0) {
        v = 1
      }
      if (v > this.total) {
        v = this.total
      }

      this.goPage(v)
      el.value = v
    },
    goPage(page) {
      if (page < 1) {
        page = 1
      }
      if (page > this.total) {
        page = this.total
      }
      this.current = page
      this.$emit('pageChange', page)
    },
    zoomUp() {
      const nscale = getCloestPlus(this.currentScale)
      this.$emit('scaleChange', nscale)
    },
    zoomDown() {
      const nscale = getCloestMinus(this.currentScale)
      this.$emit('scaleChange', nscale)
    },
    zoomCustom(e) {
      const v = parseFloat(e.target.value)
      if (!v) {
        e.target.value = this.scale
        return
      }
      let p = v / 100
      if (p < defaultScales[0]) {
        p = defaultScales[0]
      }
      if (p > defaultScales[defaultScales.length - 1]) {
        p = defaultScales[defaultScales.length - 1]
      }
      this.$emit('scaleChange', p)
    },
    keydown(e) {
      if (e.key === 'Enter') {
        this.goCustomPage()
      }
    }
  }
}
</script>
<style scoped>
.filePagesPagination i {
  font-weight: 600;
  cursor: pointer;
  margin: 0 5px;
}
.filePagesPagination .disabled {
  color: '#e8e8e8' !important;
}
.filePagesPagination .el-input__inner {
  height: 24px;
}
</style>