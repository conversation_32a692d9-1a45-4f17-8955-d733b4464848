<template>
  <div class="ruleConfigs">
    <Title>
      <span
        style="
          color: #24262a;
          font-size: 14px;
          font-weight: 600;
          margin-right: 6px;
        "
      >
        编号规则配置
      </span>
      <span
        :style="{
          color: 'red',
          marginRight: '4px'
        }"
        >*
      </span>

      <el-tooltip effect="dark" placement="top">
        <i class="olading-iconfont oi-wenhao" style="color: #7f7f7f" />
        <template #content>
          编号最多展示32位； <br />
          自定义文本支持：中英文,【】[] {} - /等字符<br />
          流水号：默认补零，超出位数后，自动进位
        </template>
      </el-tooltip>
    </Title>
    <span
      v-if="!isDisable"
      :style="{
        color: '#777C94',
        position: 'relative',
        top: '-15px',
        left: '12px'
      }"
      >可拖拽调整顺序</span
    >
    <br />
    <template>
      <DragableList :allowDrag="!isDisable && rules.length > 1" v-model="rules">
        <template v-slot="{ item, index }">
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: space-between;
            "
          >
            <div class="marinRight8">
              <!-- {{ item }} - {{ index }} -->
              <el-select
                :disabled="isDisable"
                v-model="rules[index].type"
                @change="value => handleRulesChange(value, index)"
              >
                <el-option label="自定义文本" value="1"></el-option>
                <el-option label="日期格式" value="2"></el-option>
                <el-option label="自增长流水号" value="3"></el-option>
                <el-option label="随机流水号" value="4"></el-option>
              </el-select>
            </div>
            <!-- <el-form-item :prop="`rules.`+index+`.value`" :rules="[{required:true,message:'不能为空',trigger:'blur'}]"> -->
            <div class="flex1MarinRight8" v-if="item.type === rulesTypeText">
              <el-input
                :disabled="isDisable"
                placeholder="请输入自定义文本"
                v-model="item.value"
                maxlength="20"
              />
            </div>
            <!-- </el-form-item> -->
            <!-- <el-form-item > -->
            <div class="flex1MarinRight8" v-if="item.type === rulesTypeDate">
              <el-select :disabled="isDisable" v-model="item.value">
                <el-option label="年" value="yyyy"></el-option>
                <el-option label="年月" value="yyyyMM"></el-option>
                <el-option label="年月日" value="yyyyMMdd"></el-option>
                <el-option label="年月日时分" value="yyyyMMddHHmm"></el-option>
                <el-option label="年月日时分秒" value="yyyyMMddHHmmss">
                </el-option>
              </el-select>
            </div>
            <!-- </el-form-item> -->
            <!-- <el-form-item v-if="item.type === '3'" :prop="`rules.`+index+`.value`" :rules="[{required:true,message:'不能为空',trigger:'blur'}]"> -->
            <div
              v-if="item.type === rulesTypeAutoIncrementNumber"
              class="flex1MarinRight8"
              style="display: flex"
            >
              <el-select
                class="marinRight8"
                :disabled="isDisable"
                :value="item.value + ''"
                @change="value => selfGrowChange(value, index)"
                placeholder="请选择流水位数"
              >
                <el-option label="4位" value="4"></el-option>
                <el-option label="5位" value="5"></el-option>
                <el-option label="6位" value="6"></el-option>
                <el-option label="7位" value="7"></el-option>
                <el-option label="8位" value="8"></el-option>
                <el-option label="9位" value="9"></el-option>
                <el-option label="10位" value="10"></el-option>
                <el-option label="11位" value="11"></el-option>
                <el-option label="12位" value="12"></el-option>
                <el-option label="13位" value="13"></el-option>
                <el-option label="14位" value="14"></el-option>
                <el-option label="15位" value="15"></el-option>
                <el-option label="16位" value="16"></el-option>
              </el-select>
              <el-input
                placeholder="请输入起始流水编号"
                :disabled="isDisable"
                :value="item.start"
                @input="value => selfGrowInput(value, index)"
                :style="{
                  width: '300px'
                }"
              />
            </div>
            <!-- </el-form-item> -->
            <!-- <el-form-item v-if="item.type === '4'" :prop="`rules.`+index+`.value`" :rules="[{required:true,message:'不能为空',trigger:'blur'}]"> -->
            <div
              v-if="item.type === rulesTypeRandomNumber"
              class="flex1MarinRight8"
            >
              <el-select
                :disabled="isDisable"
                v-model="item.value"
                placeholder="请选择流水位数"
              >
                <el-option label="4位" value="4"></el-option>
                <el-option label="5位" value="5"></el-option>
                <el-option label="6位" value="6"></el-option>
                <el-option label="7位" value="7"></el-option>
                <el-option label="8位" value="8"></el-option>
                <el-option label="9位" value="9"></el-option>
                <el-option label="10位" value="10"></el-option>
                <el-option label="11位" value="11"></el-option>
                <el-option label="12位" value="12"></el-option>
                <el-option label="13位" value="13"></el-option>
                <el-option label="14位" value="14"></el-option>
                <el-option label="15位" value="15"></el-option>
                <el-option label="16位" value="16"></el-option>
              </el-select>
            </div>
            <!-- </el-form-item> -->
            <i
              class="el-icon-delete"
              v-if="!isDisable && rules.length > 1"
              title="删除"
              :style="{
                cursor: 'pointer',
                width: '20px',
                height: '20px',
                fontSize: '20px'
              }"
              @click="
                () => {
                  rules.splice(index, 1)
                  $emit('delete')
                }
              "
            />
          </div>
        </template>
      </DragableList>
      <p style="color: red">{{ rulesErrorMessage }}</p>
      <el-button
        v-if="!isDisable"
        plain
        @click="
          rules.push({
            type: '1',
            value: '',
            start: ''
          })
        "
      >
        <i class="el-icon-plus" /> 新增配置项
      </el-button>
    </template>
  </div>
</template>
<script>
import Title from '../title.vue'
import DragableList from '../draggableList.vue'
import { makeIntFillZero } from '../../../pages/contract/rules/makeIntFillZero'
import {
  rulesTypeText,
  rulesTypeDate,
  rulesTypeAutoIncrementNumber,
  rulesTypeRandomNumber
} from '../../../services/contract/constants'
export default {
  components: {
    Title,
    DragableList
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    errorMessage: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      rules: [],
      rulesErrorMessage: '',
      rulesTypeText,
      rulesTypeDate,
      rulesTypeAutoIncrementNumber,
      rulesTypeRandomNumber
    }
  },
  computed: {
    isDisable() {
      return false
    }
  },
  watch: {
    // 接收回显的值
    value: {
      handler(n) {
        this.rules = n
        for (var c of this.rules) {
          if (!c.key) {
            c.key = Math.random()
          }
        }
      },
      immediate: true
    },
    // 监听组件内数据变化，使父组件同样改变
    rules: {
      handler(n) {
        this.$emit('input', n)
      },
      deep: true
    },
    errorMessage: {
      handler(n) {
        this.rulesErrorMessage = n
      }
    }
  },
  methods: {
    makeIntFillZero,
    handleRulesChange(value, index) {
      // 日期默认选中年月日
      if (value === rulesTypeDate) {
        this.rules[index].value = 'yyyyMMdd'
        this.rules[index].start = ''
      } else {
        this.rules[index].value = ''
        this.rules[index].start = ''
      }
    },
    //自增长流水号
    selfGrowChange(value, index) {
      this.rules[index].value = value
      if (!this.rules[index].start) {
        this.rules[index].start = makeIntFillZero(1, value)
      }
    },
    selfGrowInput(value, index) {
      const reg = /^\d*$/

      // debugger
      if (!value) {
        this.rules[index].start = ''
        return
      }

      const nsize = value.length
      if (reg.test(value)) {
        if (nsize > this.rules[index].value) {
          this.rules[index].start = value.slice(0, this.rules[index].value)
          return
        }
      }

      const tmp = value.split(/[1-9]/)
      if (tmp[0]) {
        var prefixAllIsZero = true
        for (var i = 0; i < tmp[0].length; i++) {
          if (value[i] !== '0') {
            prefixAllIsZero = false
            break
          }
        }
        if (prefixAllIsZero) {
          this.rules[index].start = value
          return
        }
      }

      const vint = parseInt(value, 10)
      if (Number.isNaN(vint)) {
        this.rules[index].start = ''
        return
      }

      this.rules[index].start = vint + ''
    }
  }
}
</script>
<style scoped>
.flex1MarinRight8 {
  margin-right: 8px;
  flex: 1;
}
.marinRight8 {
  margin-right: 8px;
}
::v-deep .el-select {
  width: 100%;
}
</style>