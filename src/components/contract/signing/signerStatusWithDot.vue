<template>
  <div v-if="value">
    <span
      v-if="value == Reviewing || value == Filling || value == Signing"
      class="enableWithDot underway"
    >
    </span>
    <span
      v-else-if="value == Withdrew || value == Completed"
      class="enableWithDot finish"
    ></span>
    <span v-else class="enableWithDot reject"></span>
    {{ statusMap[value] }}
  </div>
  <div v-else>-</div>
</template>
<script>
import {
  Reviewing,
  Filling,
  Signing,
  Withdrew,
  Overdue,
  Completed,
  Rejected
} from '../../../pages/contract/constants'
export default {
  props: {
    value: {
      type: String
    }
  },
  data() {
    return {
      Reviewing,
      Filling,
      Signing,
      Withdrew,
      Overdue,
      Completed,
      Rejected,
      statusMap: {
        1: '审核中',
        2: '填写中',
        3: '签署中',
        4: '已撤回',
        5: '已逾期',
        6: '已完成',
        7: '已拒绝'
      }
    }
  }
}
</script>
<style  scoped>
.enableWithDot::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  position: relative;
  left: -6px;
}
.enableWithDot.underway::before {
  background-color: #4d74fe;
}
.enableWithDot.finish::before {
  background-color: #ccc;
}
.enableWithDot.reject::before {
  background-color: #f63939;
}
</style>