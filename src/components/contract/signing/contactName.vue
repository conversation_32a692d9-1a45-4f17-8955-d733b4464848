<template>
  <div>
    <div
      @click="skipPage"
      style="
        font-weight: 500;
        font-size: 12px;
        cursor: pointer;
        color: #4f71ff;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        height: 16px;
      "
    >
      {{ row.name }}
    </div>
    <div
      style="
        font-size: 10px;
        color: #777c94;
        margin-top: 6px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        height: 16px;
      "
    >
      {{ `发起方：${row.creator.legal.name}(${row.creator.signer.name})` }}
    </div>
  </div>
</template>

<script>
import {
  ContractStatusCompleted,
  ContractStatusFilling,
  ContractStatusOverdue,
  ContractStatusRejected,
  ContractStatusReviewing,
  ContractStatusSigning,
  ContractStatusWithdrew
} from '../../../services/contract/constants'
import { user } from '../../../helpers/profile'
export default {
  name: 'ContactName',
  props: {
    //返回URL
    back: {
      type: String,
      default() {
        return ''
      }
    },
    row: {
      type: Object,
      default: () => {}
    },
    user: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    skipPage() {
      switch (this.contactStatus) {
        // 填写中
        case ContractStatusFilling:
          // 当前处理人 填写合同页面
          if (this.isHandler) {
            this.$router.push({
              path: `/contracts/${this.row.id}/write?back=${this.back}`
            })
          } else {
            // 查看合同页面
            this.$router.push({
              path: `/contracts/${this.row.id}?back=${this.back}`
            })
          }
          break
        // 签署中
        case ContractStatusSigning:
          // 当前处理人签署页面 并且有权限
          if (this.isHandler) {
            this.$router.push({
              path: `/contracts/${this.row.id}/sign?back=${this.back}`
            })
          } else {
            // 无权限 查看页面
            this.$router.push({
              path: `/contracts/${this.row.id}?back=${this.back}`
            })
          }
          break
        // 审核中
        case ContractStatusReviewing:
          // 有权限且当前处理人 审核页面
          this.$router.push({
            path: `/contracts/${this.row.id}?back=${this.back}`
          })
          break
        // 已撤回，已拒绝，已逾期   查看合同页面
        case ContractStatusWithdrew:
        case ContractStatusRejected:
        case ContractStatusOverdue:
          this.$router.push({
            path: `/contracts/${this.row.id}?back=${this.back}`
          })
          break
        // 已完成
        case ContractStatusCompleted:
          // 判断签署文件是否生成，未生成时toast提示：签署文件正在生成中，请稍后查看。文件存在时，进入查看合同页面
          this.$router.push({
            path: `/contracts/${this.row.id}?back=${this.back}`
          })
          break
        // 其他情况就是草稿
        default:
          this.$router.push({
            path: `/signings/drafts/${this.row.id}/step1/edit`,
            query: { source: 'DRAFT' }
          })
      }
    }
  },
  computed: {
    isHandler() {
      return String(this.row.handlingBy?.id) === String(user.id)
    },
    contactStatus() {
      return this.row.status
    }
  }
}
</script>

<style>
</style>