<template>
  <div>
    <template v-if="value && value.length > 0">
      <div
        style="margin-bottom: 5px; height: 14px"
        :key="index"
        v-for="(item, index) in value"
      >
        <!-- 抄送方 一维数组 无状态 无弹窗 -->
        {{ item.signer && item.signer.name }}
      </div>
    </template>
    <template v-else> - </template>
  </div>
</template>

<script>
export default {
  name: 'CarbonCopyEmpList',
  props: {
    value: {
      type: Array
    }
  }
}
</script>

<style>
</style>