
<template>
  <!-- 用于列表页草稿签署方展示，二维数组遍历 无状态 无弹窗 -->
  <div>
    <template v-for="(item, index1) in value">
      <!-- 1 个人     2 企业 -->
      <div :key="index1">
        <div :key="index2" v-for="(i, index2) in item">
          <div
            class="text-ellipsis"
            :title="
              i && i.signerType === SingerTypePerson
                ? `${i && i.signer && i.signer.name}`
                : `${i && i.legal && i.legal.name} (${
                    i && i.signer && i.signer.name
                  })`
            "
          >
            {{
              i && i.signerType === SingerTypePerson
                ? `${i && i.signer && i.signer.name}`
                : `${i && i.legal && i.legal.name} (${
                    i && i.signer && i.signer.name
                  })`
            }}
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { SingerTypePerson } from '../../../services/contract/constants'
export default {
  name: 'draftEmpList',
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      SingerTypePerson
    }
  }
}
</script>

<style>
</style>