<template>
  <el-tabs v-on="$listeners" v-bind="$attrs" style="width: 100%; border: none">
    <el-tab-pane name="ALL">
      <div slot="label" @click="groupChange()">全部合同</div>
    </el-tab-pane>
    <el-tab-pane name="HANDLE_BY_ME">
      <div slot="label" @click="groupChange('HANDLE_BY_ME')">
        待我处理 {{ handleByMe }}
      </div>
    </el-tab-pane>
    <el-tab-pane name="HANDLE_BY_OTHER">
      <div slot="label" @click="groupChange('HANDLE_BY_OTHER')">
        待他人处理 {{ handleByOther }}
      </div>
    </el-tab-pane>
    <el-tab-pane name="COMPLETE">
      <div class="dividing-line" slot="label" @click="groupChange('COMPLETE')">
        已完成
      </div>
    </el-tab-pane>
    <el-tab-pane name="RECEIVED">
      <div slot="label" @click="groupChange('RECEIVED')">我收到的</div>
    </el-tab-pane>
    <el-tab-pane name="CREATE_BY_ME">
      <div slot="label" @click="groupChange('CREATE_BY_ME')">我发起的</div>
    </el-tab-pane>
    <el-tab-pane name="CARBON_TO_ME">
      <div
        class="dividing-line"
        slot="label"
        @click="groupChange('CARBON_TO_ME')"
      >
        抄送我的
      </div>
    </el-tab-pane>
    <el-tab-pane name="DRAFT">
      <div slot="label" @click="groupChange('DRAFT')">草稿</div>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
export default {
  name: 'SignTypeTabs',
  methods: {
    groupChange(value) {
      this.$emit('groupChange', value)
    }
  },
  props: {
    handleByOtherCount: {
      type: Number,
      default: 0
    },
    handleByMeCount: {
      type: Number,
      default: 0
    }
  },
  computed: {
    handleByMe() {
      if (this.handleByMeCount) {
        return this.handleByMeCount > 99 ? `(99+)` : `(${this.handleByMeCount})`
      }
      return ''
    },
    handleByOther() {
      if (this.handleByOtherCount) {
        return this.handleByOtherCount > 99
          ? `(99+)`
          : `(${this.handleByOtherCount})`
      }
      return ''
    }
  }
}
</script>

<style scoped>
.dividing-line {
  position: relative;
}
.dividing-line::after {
  content: '';
  display: inline-block;
  position: absolute;
  height: 18px;
  width: 1px;
  background: #ceccd7;
  top: 11px;
  right: -19px;
}
</style>