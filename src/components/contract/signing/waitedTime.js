const waitedTime = waitSeconds => {
  const hours = parseInt(waitSeconds / 3600, 10)
  const remainMins = parseInt((waitSeconds - hours * 3600) / 60, 10)
  const days = parseInt(hours / 24, 10)
  const remainHours = hours - days * 24
  var r = []
  if (days) {
    r.push(`${days}天`)
    if (remainHours) {
      r.push(`${remainHours}小时`)
    }
  } else {
    if (hours) {
      r.push(`${hours}小时`)
    }
  }

  if (remainMins) {
    r.push(`${remainMins}分钟`)
  }
  if (!r.length) {
    r.push('0分钟')
  }

  return '已等待 ' + r.join('')
}

export default waitedTime