<template>
  <div class="signingDraftWaitingSignatureFiles">
    <div
      :style="{
        marginBottom: '10px',
        cursor: 'pointer',
        background: index === currentFileIndex ? '#F7FAFD' : ''
      }"
      :key="`file${index}`"
      v-for="(file, index) in files"
      @click="select(index)"
    >
      <div class="text-ellipsis-2line" :title="file.name">{{ file.name }}</div>
      <span
        v-if="requiredTotal(file.fileId) > 0"
        :style="{
          background:
            requiredCount(file.fileId) < requiredTotal(file.fileId)
              ? '#FFF3F3'
              : '#F0FBF0',
          color:
            requiredCount(file.fileId) < requiredTotal(file.fileId)
              ? '#F63939'
              : '#07BB06'
        }"
      >
        <i
          class="el-icon-circle-check"
          v-if="requiredCount(file.fileId) === requiredTotal(file.fileId)"
        />
        必填: {{ requiredCount(file.fileId) }}/{{ requiredTotal(file.fileId) }}
      </span>
      <span
        style="background: #f8f8f8; color: #777c94"
        v-if="optionalTotal(file.fileId)"
        :style="{
          background:
            optionalCount(file.fileId) < optionalTotal(file.fileId)
              ? '#F8F8F8'
              : '#F0FBF0',
          color:
            optionalCount(file.fileId) < optionalTotal(file.fileId)
              ? '#777C94'
              : '#07BB06'
        }"
      >
        <i class="el-icon-circle-check" />
        选填: {{ optionalCount(file.fileId) }}/{{ optionalTotal(file.fileId) }}
      </span>
      <span
        style="color: #ccc"
        v-if="!optionalTotal(file.fileId) && !requiredTotal(file.fileId)"
      >
        无需填写
      </span>
    </div>
  </div>
</template>
<script>
import store from '../../../helpers/store'
const fileExistField = (fileId, fieldId, pageFields = []) => {
  const pageField = pageFields.find(
    item => item.fileId === fileId && item.fieldId === fieldId
  )

  return pageField
}
export default {
  props: {
    currentFileIndex: Number,
    fields: Array,
    pageFields: Array,
    files: Array
  },
  methods: {
    requiredCountAndTotal(fileId) {
      var count = 0
      var total = 0

      for (var field of this.fields) {
        if (!field.modifiable) {
          continue
        }
        if (!fileExistField(fileId, field.id, this.pageFields)) {
          continue
        }

        if (field.writeRequired) {
          total++
          if (field.value && field.value.trim()) {
            count++
          }
        }
      }
      return [count, total]
    },
    requiredCount(fileId) {
      const [c, _] = this.requiredCountAndTotal(fileId)
      return c
    },
    requiredTotal(fileId) {
      const [_, t] = this.requiredCountAndTotal(fileId)
      return t
    },
    optionalCountAndTotal(fileId) {
      var count = 0
      var total = 0

      for (var field of this.fields) {
        if (!field.modifiable) {
          continue
        }
        if (!fileExistField(fileId, field.id, this.pageFields)) {
          continue
        }

        if (!field.writeRequired) {
          total++
          if (field.value && field.value.trim()) {
            count++
          }
        }
      }
      return [count, total]
    },
    optionalCount(fileId) {
      const [c, _] = this.optionalCountAndTotal(fileId)
      return c
    },
    optionalTotal(fileId) {
      const [_, t] = this.optionalCountAndTotal(fileId)
      return t
    },
    select(index) {
      this.$emit('select', index)
    }
  }
}
</script>