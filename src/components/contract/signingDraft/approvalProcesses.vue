<template>
  <div class="approvalProcesses">
    <div v-if="!steps || !steps.length">暂无流程</div>
    <StepsWithDashedLine :steps="steps">
      <template v-slot="{ item }">
        <div style="display: flex; justify-content: space-between">
          <b>{{ item.title }}</b>
        </div>
        <div v-if="item.company">{{ item.company }}</div>
        <div style="color: #777c94">{{ item.user }} ({{ item.mobile }})</div>
      </template>
    </StepsWithDashedLine>
  </div>
</template>
<script>
import StepsWithDashedLine from '../stepsWithDashedLine.vue'
export default {
  components: {
    StepsWithDashedLine
  },
  props: {
    creator: Object,
    users: {
      type: Array,
      default() {
        return []
      }
    }
  },
  computed: {
    steps() {
      var r = []
      if (!this.users) {
        return r
      }
      r.push({
        title: '发起签署',
        company: this.creator.legal.name,
        user: this.creator.signer.name,
        mobile: this.creator.signer.mobile
      })
      for (var c of this.users) {
        r.push({
          title: '审核',
          user: c.name,
          mobile: c.mobile
        })
      }

      return r
    }
  },
  data() {
    return {}
  }
}
</script>