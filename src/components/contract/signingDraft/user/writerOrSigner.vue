<template>
  <div class="users" style="display: flex" :title="title">
    <Icon :type="companyName ? SingerTypeCompany : SingerType<PERSON>erson" />
    <div style="flex: 1">
      <b v-if="companyName">
        {{ companyName }}
      </b>
      <div v-if="companyName" style="color: #777c94">
        {{ cusers[0].signer.name }} ({{ cusers[0].signer.mobile }})
        <span v-if="length > 1">等{{ length }}人</span>
      </div>
      <b v-if="!companyName">
        {{ cusers[0].signer.name }} ({{ cusers[0].signer.mobile }})
        <span v-if="length > 1">等{{ length }}人</span>
      </b>
    </div>
  </div>
</template>
<script>
import {
  SingerTypePerson,
  SingerTypeCompany
} from '../../../../services/contract/constants'
import Icon from './icon.vue'
export default {
  created() {
    console.log('users=', this.users)
    for (var key in this.users) {
      this.cusers.push(this.users[key])
    }
  },
  components: {
    Icon
  },
  computed: {
    length() {
      return Object.keys(this.users).length
    },
    title() {
      if (this.length <= 1) {
        return ''
      }
      var r = []
      for (var key in this.users) {
        var c = this.users[key]
        r.push(c.signer.name)
      }
      return r.join(', ')
    },
    companyName() {
      for (var key in this.users) {
        var c = this.users[key]
        if (c.legal) {
          return c.legal.name
        }
      }
      return ''
    }
  },
  props: {
    users: Array
  },
  data() {
    return {
      cusers: [],
      SingerTypePerson,
      SingerTypeCompany
    }
  }
}
</script>