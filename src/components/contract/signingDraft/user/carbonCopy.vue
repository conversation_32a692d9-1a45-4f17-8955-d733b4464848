<template>
  <div class="user" style="display: flex">
    <Icon :type="user.signerType" />
    <div
      style="
        flex: 1;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
      "
    >
      <span style="font-weight: 400; display: inline-block; width: 170px">
        {{ user.signer.name }} ({{ user.signer.mobile }})
      </span>
      <span style="color: rgb(168, 172, 186)">{{
        user.received ? '已查看' : '未查看'
      }}</span>
    </div>
  </div>
</template>
<script>
import Icon from './icon.vue'
export default {
  components: {
    Icon
  },
  props: {
    user: Object
  }
}
</script>