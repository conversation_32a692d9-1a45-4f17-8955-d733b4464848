<template>
  <div>
    <AttachmentList
      :readOnly="readOnly"
      v-on="$listeners"
      v-bind="$attrs"
      :files="value"
    />
    <Uploader
      @handleSuccess="handleAccessorySuccess"
      @handleUplaodError="handleUplaodError"
      v-if="!readOnly"
    >
      <el-button style="margin-top: 10px" plain type="primary">
        <i class="el-icon-plus" /> 添加附件
      </el-button>
    </Uploader>
  </div>
</template>
<script>
import AttachmentList from './attachmentList.vue'
import Uploader from '../../../pages/contract/templatesNewStep1/uploader.vue'
import handleError from '../../../helpers/handleError'
import handleSuccess from '../../../helpers/handleSuccess'
export default {
  props: {
    value: {
      type: Array,
      default() {
        return []
      }
    },
    readOnly: {
      type: <PERSON>olean,
      default: false
    }
  },
  components: {
    Uploader,
    AttachmentList
  },
  methods: {
    handleAccessorySuccess(res, file, fileList) {
      if (!res.success) {
        handleError({ message: res.message })
        return
      }
      let n = [...this.value]
      // 上传成功后替换成真实的数据
      n.push({ ...res.data })
      this.$emit('input', n)
      this.$emit('saveAttachment')
      handleSuccess('文件上传成功')
    },
    handleUplaodError() {
      handleError({ message: '文件上传失败' })
    }
    // handleAccessoryProgress(res, file, fileList) {
    //   const n = [...this.value]
    //   console.log(res, file, fileList)
    //   const currentFile = n.find(curFile => curFile.uid === file.uid)
    //   if (currentFile) {
    //     currentFile.percent = res.percent
    //   } else {
    //     n.push({
    //       name: file.name,
    //       uid: file.uid,
    //       key: Math.random(),
    //       percent: res.percent
    //     })
    //     this.$emit('input', n)
    //   }
    // },
    // 附件上传成功
    // handleAccessorySuccess(res, file, fileList) {
    //   let n = [...this.value]
    //   if (!res.success) {
    //     setTimeout(() => {
    //       n = n.filter(fileList => fileList.uid !== file.uid)
    //     }, 500)
    //     handleError({ message: res.message })
    //     return
    //   }
    //   // 上传成功后替换成真实的数据
    //   n = n.map(fileList => {
    //     if (fileList.uid === file.uid) {
    //       return {
    //         ...fileList,
    //         archiveId: res?.data?.archiveId,
    //         name: res?.data?.name,
    //         size: res?.data?.size
    //       }
    //     } else {
    //       return fileList
    //     }
    //   })
    //   this.$emit('input', n)
    //   this.$emit('saveAttachment')
    //   handleSuccess('文件上传成功')
    // },
    // handleUplaodError(res, file, fileList) {
    //   console.log(res, file, 'res, fileres, fileres, file')
    //   let n = [...this.value]
    //   console.log(n, 'nnnnnnnnn')
    //   n = n.filter(fileList => fileList.uid !== file.uid)
    //   this.$emit('input', n)
    //   handleError({ message: '上传失败' })
    //   return
    // }
  }
}
</script>
<style scoped>
.senderAttachmentFiles {
  margin-top: 10px;
}
</style>