<template>
  <div
    :style="{
      cursor: 'pointer',
      border: '1px solid #ccc',
      width: width,
      borderRadius: '5px',
      textAlign: width === '100px' ? 'center' : '',
      margin: '0 4px',
      padding: '0 4px',
      whiteSpace: 'nowrap',
      textOverflow: 'ellipsis',
      overflow: 'hidden',
      display: 'inline-block',
      height: '28px',
      lineHeight: '28px',
      fontSize: '12px'
    }"
    :title="field.name"
  >
    {{ field.name }}
  </div>
</template>
<script>
export default {
  props: {
    width: {
      type: String,
      default() {
        return '100px'
      }
    },
    field: {
      type: Object,
      validator(v) {
        if (!v || !v.name) {
          return false
        }

        return true
      }
    }
  }
}
</script>