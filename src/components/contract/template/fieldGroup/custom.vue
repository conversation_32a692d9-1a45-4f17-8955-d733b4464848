<template>
  <div>
    <div
      :style="{
        marginBottom: '10px'
      }"
    >
      <el-button
        plain
        :style="{
          marginLeft: '10px',
          height: '25px',
          lineHeight: '14px',
          padding: '2px 5px',
          fontSize: '12px'
        }"
        @click="dialog.create = true"
      >
        <i class="el-icon-plus" /> 新增字段
      </el-button>
    </div>
    <div
      :style="{
        display: 'flex',
        flexFlow: 'wrap'
      }"
    >
      <FieldFieldContainer
        :key="field.name"
        v-for="field in filteredFields"
        :currentScale="$attrs.currentScale"
        :field="field"
        :type="FieldTypeCustom"
      >
        <div
          :style="{
            position: 'relative'
          }"
        >
          <Field :field="field" />
          <i
            class="el-icon-error"
            :style="{
              position: 'absolute',
              top: '-5px',
              right: '0px',
              color: 'red'
            }"
            @click="openDelDialog(field)"
          />
        </div>
      </FieldFieldContainer>
    </div>
    <span v-if="!filteredFields.length && this.filter">暂无搜索内容</span>
    <el-dialog
      :visible.sync="dialog.create"
      title="新建"
      @open="openForm"
      @close="closeForm"
      :close-on-click-modal="false"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        @submit.native.prevent="create"
      >
        <el-form-item prop="name">
          <el-input v-model.trim="form.name" placeholder="请输入名称" />
          <br />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialog.create = false"> 取消 </el-button>
        <el-button type="primary" @click="create"> 提交 </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="`确认要删除【${currentField.name}】吗？`"
      :visible.sync="dialog.normal"
      width="480px"
      :close-on-click-modal="false"
    >
      <span>
        从自定义字段区删除，已经设置的该字段填充域，同时会被删除
        <div
          slot="footer"
          class="dialog-footer"
          style="margin-top: 60px; text-align: right"
        >
          <el-button @click="dialog.normal = false">取消</el-button>
          <el-button type="primary" @click="del"> 确定 </el-button>
        </div>
      </span>
    </el-dialog>
    <el-dialog
      title="请选择删除字段的方式"
      :visible.sync="dialog.common"
      width="480px"
      @close="closeCommon"
      :close-on-click-modal="false"
    >
      <el-checkbox
        v-model="checkbox.normal"
        label="从当前模板删除"
      ></el-checkbox>
      <br />
      <span>模板中已设置的该字段填充域，全部会被删除</span>
      <br />
      <el-checkbox
        v-model="checkbox.common"
        label="从常用自定义字段中删除"
      ></el-checkbox>
      <br />
      <span>
        删除后，该字段不会出现在未使用该字段的模板自定义区，不会影响已经使用此字段的模板
      </span>
      <div
        slot="footer"
        class="dialog-footer"
        style="margin-top: 60px; text-align: right"
      >
        <el-button @click="dialog.common = false">取消</el-button>
        <el-button type="primary" @click="del"> 确定 </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import FieldFieldContainer from './fieldContainer.vue'
import Field from './field.vue'
import handleError from '../../../../helpers/handleError'
import { FieldTypeCustom } from '../../../../services/contract/constants'
export default {
  components: {
    FieldFieldContainer,
    Field
  },
  computed: {
    filteredFields() {
      if (!this.filter) {
        return this.fields
      }
      return this.fields.filter(item => item.name.includes(this.filter))
    }
  },
  data() {
    return {
      FieldTypeCustom,
      form: { name: '' },
      dialog: {
        normal: false,
        common: false,
        create: false
      },
      currentField: {},
      checkbox: {
        normal: false,
        common: false
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入名称',
            trigger: 'blur'
          },
          {
            max: 20,
            message: '名称不可超过20字',
            trigger: 'blur'
          },
          {
            validator: (rule, value, callback) => {
              if (this.fields.find(item => item.name === value)) {
                callback(`${value}已存在`)
                return
              }
              for (var g of this.otherFieldGroups) {
                if (g.fieldList.find(item => item.name === value)) {
                  callback(`${value}已存在`)
                  return
                }
              }
              callback()
            }
          }
        ]
      }
    }
  },
  methods: {
    openDelDialog(field) {
      this.currentField = field
      if (field.common) {
        this.dialog.common = true
      } else {
        this.dialog.normal = true
      }
    },
    del() {
      if (!this.checkbox.normal && !this.checkbox.common) {
        handleError('请至少选择一项')
        return
      }

      this.dialog.normal = false
      this.dialog.common = false
      this.$emit(
        'del',
        this.currentField,
        this.checkbox.normal,
        this.checkbox.common
      )
    },
    create() {
      this.$refs.form.validate(valid => {
        if (!valid) {
          this.scrollIntoError(this.$refs.form)
          return
        }
        this.$emit('add', this.form.name.trim())
        this.dialog.create = false
      })
    },
    openForm() {
      if (this.$refs.form) {
        this.$refs.form.clearValidate()
      }
    },
    closeForm() {
      this.form.name = ''
    },
    closeCommon() {
      this.checkbox = {
        normal: false,
        common: false
      }
    }
  },
  props: {
    filter: String,
    otherFieldGroups: Array,
    fields: {
      type: Array,
      validator(v) {
        for (var c of v) {
          if (!c.name) {
            return false
          }
        }
        return true
      }
    }
  }
}
</script>
<style scoped>
.fieldContainer:hover i.el-icon-error {
  display: inherit;
}
.fieldContainer i.el-icon-error {
  display: none;
}
</style>