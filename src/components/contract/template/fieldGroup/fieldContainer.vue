<template>
  <div
    class="fieldContainer"
    draggable
    @mousedown="mousedown"
    @dragend="dragend"
    @dragstart="dragstart"
  >
    <slot></slot>
  </div>
</template>
<script>
import {
  PageFieldDefaultWidth,
  PageFieldDefaultHeight,
  PageFieldDefaultFontSize,
  PageFieldDefaultBorderColor
} from '../../../../pages/contract/constants'
import {
  FieldTypeCustom,
  FieldTypeCompany,
  FieldTypeDate
} from '../../../../services/contract/constants'
import isSignatureFieldType from '../isSignatureFieldType'
var mouseX = 0
var mouseY = 0
var lastDragFieldEl = null
export default {
  props: {
    field: Object,
    type: {
      type: String,
      validator(v) {
        if (!v.trim()) {
          return false
        }
        return true
      }
    },
    currentScale: Number,
    //用于判断是否为公司合同信息组下field
    fieldGroupName: String,
    getDragImage: Function
  },
  data() {
    return {
      FieldTypeCustom
    }
  },
  methods: {
    mousedown(e) {
      var rect = e.target.getBoundingClientRect()
      mouseX = e.clientX - rect.left
      mouseY = e.clientY - rect.top
    },
    dragend() {
      if (lastDragFieldEl) {
        lastDragFieldEl.remove()
      }
    },
    dragstart(e) {
      e.dataTransfer.setData(
        'field',
        JSON.stringify({
          ...this.field,
          type: this.type,
          fieldGroupName: this.fieldGroupName,
          mouseX,
          mouseY,
          isCustomControl: this.type === FieldTypeCustom
        })
      )

      const currentScale = this.currentScale
      if (isSignatureFieldType(this.type)) {
        lastDragFieldEl = document.createElement('div')
        lastDragFieldEl.id = Math.random()
        lastDragFieldEl.style.position = 'absolute'
        lastDragFieldEl.style.left = '-1000px'
        // lastDragFieldEl.style.left = '0'
        // lastDragFieldEl.style.top = '0'
        lastDragFieldEl.style.height = ''
        lastDragFieldEl.style.width = ''
        lastDragFieldEl.style.borderRadius = '4px'
        lastDragFieldEl.style.border = `1px solid ${PageFieldDefaultBorderColor}`
        lastDragFieldEl.style.background = `${PageFieldDefaultBorderColor}20`
        var t = this.getDragImage('person')
        // var t = this.personEl.$el
        if (this.field.type === FieldTypeCompany) {
          t = this.getDragImage('company')
          // t = this.companyEl.$el
        } else if (this.field.type === FieldTypeDate) {
          t = this.getDragImage('date')
          // t = this.dateEl.$el
        }
        t = t.$el

        var cel = t.cloneNode(true)
        const cw = parseInt(cel.style.width, 10)
        const ch = parseInt(cel.style.height, 10)
        cel.style.height = `${ch}px`
        cel.style.width = `${cw}px`
        cel.style.zoom = `${currentScale}`
        cel.style.fontSize = `${PageFieldDefaultFontSize}px`

        lastDragFieldEl.appendChild(cel)
        document.body.appendChild(lastDragFieldEl)

        e.dataTransfer.setDragImage(lastDragFieldEl, mouseX, mouseY)

        return
      }

      lastDragFieldEl = document.createElement('div')
      lastDragFieldEl.id = Math.random()
      lastDragFieldEl.innerText = this.field.name
      lastDragFieldEl.style.position = 'absolute'
      lastDragFieldEl.style.left = '-1000px'
      lastDragFieldEl.style.zoom = `${currentScale}`
      lastDragFieldEl.style.border = `1px solid ${PageFieldDefaultBorderColor}`
      lastDragFieldEl.style.background = `${PageFieldDefaultBorderColor}20`
      lastDragFieldEl.style.height = `${PageFieldDefaultHeight}px`
      var w = PageFieldDefaultWidth
      var p = this.field.name.length * PageFieldDefaultFontSize
      if (w < p) {
        w = p
      }
      lastDragFieldEl.style.width = `${w}px`
      lastDragFieldEl.style.width = `${w}px`
      lastDragFieldEl.style.borderRadius = `4px`
      lastDragFieldEl.style.paddingLeft = `5px`
      lastDragFieldEl.style.fontSize = `${PageFieldDefaultFontSize}px`
      lastDragFieldEl.style.minWidth = ``
      lastDragFieldEl.style.minHeight = ``

      document.body.appendChild(lastDragFieldEl)

      e.dataTransfer.setDragImage(lastDragFieldEl, mouseX, mouseY)
    }
  }
}
</script>