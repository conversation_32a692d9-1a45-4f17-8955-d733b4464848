<template>
  <div
    :style="{
      display: 'flex',
      flexFlow: 'wrap'
    }"
  >
    <FieldContainer
      :key="field.name"
      v-for="field in filteredFields"
      :currentScale="$attrs.currentScale"
      :field="field"
      :type="'4'"
      :fieldGroupName="fieldGroupName"
    >
      <Field :field="field" />
    </FieldContainer>
    <span v-if="!filteredFields.length && this.filter">暂无搜索内容</span>
    <span v-if="!filteredFields.length && !this.filter">暂无数据内容展示</span>
  </div>
</template>
<script>
import FieldContainer from './fieldContainer.vue'
import Field from './field.vue'
export default {
  components: {
    FieldContainer,
    Field
  },
  computed: {
    filteredFields() {
      if (!this.filter) {
        return this.fields
      }
      return this.fields.filter(item => item.name.includes(this.filter))
    }
  },
  props: {
    filter: String,
    fieldGroupName: String,
    fields: {
      type: Array,
      validator(v) {
        for (var c of v) {
          if (!c.name) {
            return false
          }
        }
        return true
      }
    }
  }
}
</script>