<template>
  <div
    class="fieldTypeDate"
    :style="{
      textAlign: 'center',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: `${pageField && pageField.height ? pageField.height : 24}px`,
      width: `${pageField && pageField.width ? pageField.width : 158}px`,
      fontSize: '12px'
    }"
  >
    <div v-if="value">{{ value }}</div>
    <div
      v-if="!value"
      :style="{
        textAlign: 'center'
      }"
    >
      {{ pageField.dateFormat }}
    </div>
  </div>
</template>
<script>
export default {
  props: {
    value: '',
    pageField: {
      type: Object,
      default() {
        return {
          dateFormat: 'yyyy年MM月dd日'
        }
      }
    }
  }
}
</script>