<template>
  <span
    :style="styles"
    v-html="field.value ? field.value.replaceAll('\n', '<br/>') : field.name"
  />
</template>
<script>
import { TextareaLineHeight } from '../../../../pages/contract/constants'
export default {
  computed: {
    styles() {
      var styles = {
        color: this.field.value ? '' : '#979797'
      }
      if (this.field.value && this.field.value.indexOf('\n') !== -1) {
        styles.lineHeight = TextareaLineHeight
      }

      return styles
    }
  },
  props: {
    field: Object
  }
}
</script>