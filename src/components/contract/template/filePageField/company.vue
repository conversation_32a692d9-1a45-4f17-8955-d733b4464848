<template>
  <div
    :style="{
      height: `${pageField ? pageField.height : 158}px`,
      width: `${pageField ? pageField.width : 158}px`,
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    }"
  >
    <div
      v-if="!value"
      :style="{
        textAlign: 'center'
      }"
    >
      <img :src="icon" height="24" width="24" />
      <br />
      企业印章
    </div>
    <img
      v-if="value"
      :src="value"
      :style="{
        objectFit: 'scale-down',
        height: `${pageField ? pageField.height : 158}px`,
        width: `${pageField ? pageField.width : 158}px`
      }"
    />
  </div>
</template>
<script>
import icon from '../../../../assets/images/icon/<EMAIL>'
export default {
  props: {
    value: String,
    pageField: Object
  },
  data() {
    return {
      icon
    }
  }
}
</script>